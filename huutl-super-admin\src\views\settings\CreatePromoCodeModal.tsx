import { useForm, type Resolver } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useEffect } from "react";

import Button from "../../components/formElements/Button";

import InputWrapper from "../../components/formElements/InputWrapper";
import Textbox from "../../components/formElements/Textbox";

import type {
	ICreateEditPromoCodeModalProps,
	IPromoCodeFormData,
} from "../../interfaces/promoCodeInterface";
import { promoCodeSchema } from "../../validationSchemas/promoCodeValidations";
import { useTranslation } from "react-i18next";
import CommonModal from "../../components/commonModals/CommonModal";

const CreateEditPromoCodeModal = ({
	isOpen,
	onClose,
	onSubmit: onSubmitCallback,
	isSubmitting = false,
	isEditMode = false,
	editData = null,
}: ICreateEditPromoCodeModalProps) => {
	const { t } = useTranslation();
	const {
		control,
		handleSubmit,
		reset,
		formState: { errors },
	} = useForm<IPromoCodeFormData>({
		resolver: yupResolver(promoCodeSchema(t)) as Resolver<IPromoCodeFormData>,
		mode: "onChange",
		defaultValues: {
			name: "",
			discountValue: 1,
			validFrom: "",
			validUntil: "",
		},
	});

	// Get today's date in YYYY-MM-DD format for min date attribute
	const getTodayDateString = () => {
		const today = new Date();
		const year = today.getFullYear();
		const month = String(today.getMonth() + 1).padStart(2, "0");
		const day = String(today.getDate()).padStart(2, "0");
		return `${year}-${month}-${day}`;
	};

	const minDate = getTodayDateString();

	// Convert ISO datetime to YYYY-MM-DD format for date input
const formatDateForInput = (dateString: string): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const day = String(date.getUTCDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
};

	// Reset form when modal opens or edit data changes
	useEffect(() => {
		if (isOpen) {
			if (isEditMode && editData) {
				// Edit mode: populate with existing data
				reset({
					name: editData.name,
					discountValue: editData.discount_value,
					validFrom: formatDateForInput(editData.valid_from),
					validUntil: formatDateForInput(editData.valid_until),
				});
			} else {
				// Create mode: reset to empty
				reset({
					name: "",
					discountValue: 1,
					validFrom: "",
					validUntil: "",
				});
			}
		}
	}, [isOpen, isEditMode, editData, reset]);

	// Handle form submission
	const onSubmit = (data: IPromoCodeFormData) => {
		onSubmitCallback(data);
	};

	// Handle modal close
	const handleClose = () => {
		if (isSubmitting) return;
		reset();
		onClose();
	};

	const modalTitle = isEditMode
		? t("edit_promo_code")
		: t("create_new_promo_code");

	const modalDescription = isEditMode
		? t("edit_promo_code_dates_description")
		: t("create_a_new_discount_code_that_customers_can_use_during_plan_purchase");

	const submitButtonText = isEditMode
		? t("update_promo_code")
		: t("create_promo_code");

	return (
		<CommonModal
			isOpen={isOpen}
			onClose={handleClose}
			title={modalTitle}
			description={modalDescription}
			showCrossIcon={false}
			isSubmitting={isSubmitting}
		>
			<form onSubmit={handleSubmit(onSubmit)}>
				<div className="modal-body">
					<div className="row g-3">
						{/* Promo Code Name */}
						<div className="col-md-6">
							<InputWrapper>
								<InputWrapper.Label htmlFor="name">
									{t("name")}
								</InputWrapper.Label>
								<Textbox
									control={control}
									name="name"
									id="name"
									type="text"
									className="form-control"
									placeholder={t("enter_promo_code_name")}
									disabled={isSubmitting || isEditMode}
								/>
								<InputWrapper.Error message={errors.name?.message} />
							</InputWrapper>
						</div>

						{/* Discount Value */}
						<div className="col-md-6">
							<InputWrapper>
								<InputWrapper.Label htmlFor="discountValue">
									{t("discount_value")} (%)
								</InputWrapper.Label>
								<Textbox
									control={control}
									name="discountValue"
									id="discountValue"
									type="number"
									className="form-control"
									placeholder="25"
									min="1"
									max="100"
									disabled={isSubmitting || isEditMode}
								/>
								<InputWrapper.Error message={errors.discountValue?.message} />
							</InputWrapper>
						</div>

						{/* Valid From */}
						<div className="col-md-6">
							<InputWrapper>
								<InputWrapper.Label htmlFor="validFrom">
									{t("valid_from")}
								</InputWrapper.Label>
								<Textbox
									control={control}
									name="validFrom"
									id="validFrom"
									type="date"
									className="form-control"
									disabled={isSubmitting}
									min={minDate}
								/>
								<InputWrapper.Error message={errors.validFrom?.message} />
							</InputWrapper>
						</div>

						{/* Valid Until */}
						<div className="col-md-6">
							<InputWrapper>
								<InputWrapper.Label htmlFor="validUntil">
									{t("valid_until")}
								</InputWrapper.Label>
								<Textbox
									control={control}
									name="validUntil"
									id="validUntil"
									type="date"
									className="form-control"
									disabled={isSubmitting}
									min={minDate}
								/>
								<InputWrapper.Error message={errors.validUntil?.message} />
							</InputWrapper>
						</div>
					</div>
				</div>
				<div className="modal-footer">
					<div className="action-btn">
						<Button
							type="button"
							className="grey-outline-btn radius-md"
							onClick={handleClose}
							disabled={isSubmitting}
						>
							{t("cancel")}
						</Button>
						<Button
							type="submit"
							className="primary-btn radius-md"
							disabled={isSubmitting}
						>
							{submitButtonText}
							{isSubmitting && (
								<span
									className="spinner-border spinner-border-sm ms-2"
									role="status"
									aria-hidden="true"
								></span>
							)}
						</Button>
					</div>
				</div>
			</form>
		</CommonModal>
	);
};

export default CreateEditPromoCodeModal;
