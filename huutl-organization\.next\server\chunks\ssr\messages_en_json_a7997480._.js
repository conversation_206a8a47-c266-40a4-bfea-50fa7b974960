module.exports = [
"[project]/messages/en.json (json)", ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"welcome\":\"Welcome\",\"description\":\"This is a sample translation\",\"hello\":\"Hello\",\"goodbye\":\"Goodbye\",\"welcome_to_huutl\":\"Welcome to Huutl!\",\"ai_business_partner\":\"Your 24/7 AI Business Partner™ — Driving revenue and automating your operations.\",\"hello_welcome\":\"Hello! Welcome\",\"sign_in_to_account\":\"Sign in to your admin account\",\"something_went_wrong\":\"Something went wrong\",\"email\":\"Email\",\"password\":\"Password\",\"remember_me\":\"Remember Me\",\"forgot_password\":\"Forgot Password\",\"sign_in\":\"Sign In\",\"enter_your_email\":\"Enter your email\",\"enter_your_password\":\"Enter your password\",\"email_is_required\":\"Email is required\",\"password_is_required\":\"Password is required\",\"please_enter_valid_email_address\":\"Please enter a valid email address\",\"please_enter_valid_password\":\"Please enter valid password\",\"don_t_have_an_account\":\"Don't have an account?\",\"sign_up\":\"Sign Up\",\"name\":\"Name\",\"name_is_required\":\"Name is required\",\"name_must_be_at_least_2_characters_long\":\"Name must be at least 2 characters long\",\"name_must_be_at_most_25_characters_long\":\"Name must be at most 25 characters long\",\"password_check_regex\":\"Password must be between 8 and 16 characters long and include at least 1 uppercase letter, 1 lowercase letter, 1 numeric digit, and 1 special character\",\"confirm_password\":\"Confirm Password\",\"confirm_password_is_required\":\"Confirm Password is required\",\"passwords_must_match\":\"Password does not match\",\"enter_your_name\":\"Enter your name\",\"confirm_your_password\":\"Confirm your password\",\"email_val_msg\":\"Enter a valid email address\",\"valid_otp\":\"Enter a valid 6-digit code\",\"org_name_req\":\"Organization name is required\",\"org_name_alpha\":\"Organization name must contain only alphabets and numbers\",\"org_name_min\":\"Organization name must be at least 3 characters\",\"org_name_max\":\"Organization name must not exceed 50 characters\",\"organization_name\":\"Organization Name\",\"enter_your_org_name\":\"Enter your organization name\",\"phone_req\":\"Phone number is required\",\"phone_number\":\"Phone Number\",\"phone_format\":\"Phone number must be in format: +1 (XXX) XXX-XXXX\",\"otp_req\":\"Code is required\",\"verification_code_length_invalid\":\"Verification code must be 6 digits\",\"verification_code_numeric_only\":\"Verification code must contain only numbers\",\"admin\":\"Admin\",\"staff\":\"Staff\",\"login_success\":\"Login successful\",\"otp_sent_successfully\":\"Code sent successfully\",\"invalid_session_please_try_again\":\"Session expired please try again\",\"logout\":\"Logout\",\"logout_description\":\"Are you sure you want to logout?\",\"confirm\":\"Confirm\",\"cancel\":\"Cancel\"}"));}),
];