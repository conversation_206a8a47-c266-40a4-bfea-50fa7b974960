# 📝 Exact Changes Made

## 🎯 Summary

Implemented smart routing logic to open Edit Modal when user clicks "Activate" on an expired promo code, allowing users to update only the dates while keeping name and discount disabled.

---

## 📁 File 1: PromoCode.tsx

### Change 1: Updated Imports
```typescript
// BEFORE
import CreatePromoCodeModal from "./CreatePromoCodeModal";

// AFTER
import CreateEditPromoCodeModal from "./CreatePromoCodeModal";
import { updatePromoCodeService } from "../../services/promoCodeService";
```

### Change 2: Added State for Edit Mode
```typescript
// ADDED
const [isEditMode, setIsEditMode] = useState(false);
const [editPromoData, setEditPromoData] = useState<IPromoCode | null>(null);
```

### Change 3: Added Edit Submission Handler
```typescript
// ADDED
const handlePromoCodeEditSubmit = async (data: IPromoCodeFormData) => {
    if (!editPromoData) return;
    try {
        setLoading((prev) => ({ ...prev, isSubmitting: true }));
        const validFromDate = new Date(data.validFrom + TIMEZONE_OFFSET);
        const validUntilDate = new Date(data.validUntil + TIMEZONE_OFFSET_END);
        const updateData = {
            valid_from: validFromDate.toISOString(),
            valid_until: validUntilDate.toISOString(),
        };
        const response = await updatePromoCodeService(editPromoData.id, updateData);
        if (response?.success) {
            toastMessageSuccess(response?.message);
            setIsPromoModalOpen(false);
            setIsEditMode(false);
            setEditPromoData(null);
            fetchPromoCodes(true);
        } else {
            toastMessageError(response.message);
        }
    } catch (error) {
        console.error("Error updating promo code:", error);
        toastMessageError(t("something_went_wrong"));
    } finally {
        setLoading((prev) => ({ ...prev, isSubmitting: false }));
    }
};
```

### Change 4: Updated handleStatusChangeClick with Smart Routing
```typescript
// BEFORE
const handleStatusChangeClick = (promoCodeId, action) => {
    setSelectedPromoCodeId(promoCodeId);
    setStatusChangeAction(action);
    setIsStatusChangeModalOpen(true);
};

// AFTER
const handleStatusChangeClick = (promoCodeId, action) => {
    const promo = promoCodesData.find((p) => p.id === promoCodeId);
    if (promo && promo.expired && action === PROMO_CODE_ACTION.ACTIVATE) {
        setEditPromoData(promo);
        setIsEditMode(true);
        setIsPromoModalOpen(true);
    } else {
        setSelectedPromoCodeId(promoCodeId);
        setStatusChangeAction(action);
        setIsStatusChangeModalOpen(true);
    }
};
```

### Change 5: Updated Modal Usage
```typescript
// BEFORE
<CreatePromoCodeModal
    isOpen={isPromoModalOpen}
    onClose={() => setIsPromoModalOpen(false)}
    onSubmit={handlePromoCodeSubmit}
    isSubmitting={loading.isSubmitting}
/>

// AFTER
<CreateEditPromoCodeModal
    isOpen={isPromoModalOpen}
    onClose={() => {
        setIsPromoModalOpen(false);
        setIsEditMode(false);
        setEditPromoData(null);
    }}
    onSubmit={isEditMode ? handlePromoCodeEditSubmit : handlePromoCodeSubmit}
    isSubmitting={loading.isSubmitting}
    isEditMode={isEditMode}
    editData={editPromoData}
/>
```

### Change 6: Removed Edit Button from Actions Column
```typescript
// REMOVED
{item.status === PROMO_CODE_STATUS.ACTIVE && !item.expired ? (
    <Button onClick={() => handleEditClick(item.id)}>
        {t("edit")}
    </Button>
) : null}
```

---

## 📁 File 2: CreatePromoCodeModal.tsx

### Change 1: Component Renamed
```typescript
// BEFORE
const CreatePromoCodeModal = ({ ... }) => {

// AFTER
const CreateEditPromoCodeModal = ({ ... }) => {
```

### Change 2: Updated Props Interface
```typescript
// ADDED
interface ICreateEditPromoCodeModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: IPromoCodeFormData) => void;
    isSubmitting?: boolean;
    isEditMode?: boolean;
    editData?: IPromoCode | null;
}
```

### Change 3: Added Date Formatting Function
```typescript
// ADDED
const formatDateForInput = (dateString: string): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
};
```

### Change 4: Updated useEffect to Handle Edit Mode
```typescript
// ADDED
useEffect(() => {
    if (isOpen) {
        if (isEditMode && editData) {
            reset({
                name: editData.name,
                discountValue: editData.discount_value,
                validFrom: formatDateForInput(editData.valid_from),
                validUntil: formatDateForInput(editData.valid_until),
            });
        } else {
            reset({
                name: "",
                discountValue: 1,
                validFrom: "",
                validUntil: "",
            });
        }
    }
}, [isOpen, isEditMode, editData, reset]);
```

### Change 5: Disabled Name Field in Edit Mode
```typescript
// BEFORE
<Textbox control={control} name="name" disabled={isSubmitting} />

// AFTER
<Textbox control={control} name="name" disabled={isSubmitting || isEditMode} />
```

### Change 6: Disabled Discount Field in Edit Mode
```typescript
// BEFORE
<Textbox control={control} name="discountValue" disabled={isSubmitting} />

// AFTER
<Textbox control={control} name="discountValue" disabled={isSubmitting || isEditMode} />
```

### Change 7: Dynamic Modal Title
```typescript
// ADDED
const modalTitle = isEditMode ? t("edit_promo_code") : t("create_new_promo_code");
const submitButtonText = isEditMode ? t("update_promo_code") : t("create_promo_code");
```

---

## 📁 File 3: promoCodeService.ts

### Change: Added Update Service
```typescript
// ADDED
export const updatePromoCodeService = async (
    promoCodeId: number,
    data: { valid_from: string; valid_until: string }
): Promise<ApiResponse> => {
    return await patch(
        `${endpoint.promoCodes.UPDATE_PROMO_CODE_STATUS.replace("/status", "")}/${promoCodeId}`,
        data
    );
};
```

---

## 📁 File 4: promoCodeInterface.ts

### Change: Added New Interfaces
```typescript
// ADDED
export interface IPromoCodeUpdateDatesRequest {
    id: number;
    valid_from: string;
    valid_until: string;
}

export interface ICreateEditPromoCodeModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: IPromoCodeFormData) => void;
    isSubmitting?: boolean;
    isEditMode?: boolean;
    editData?: IPromoCode | null;
}
```

---

## 📁 File 5: translation.json

### Change: Added Translation Keys
```json
// ADDED
"edit_promo_code": "Edit Promo Code",
"edit_promo_code_dates_description": "Edit the valid from and valid until dates for this promo code",
"update_promo_code": "Update Promo Code",
"edit": "Edit"
```

---

## 📁 File 6: repository.py (Backend)

### Change: Added Update Function
```python
# ADDED
async def update_promo_code(
    db: AsyncSession, promo_code_id: int, promo_code_data: PromoCodeCreate
) -> ResponseModal:
    """Update promo code dates with validation"""
    # Validates: exists, not expired, is active
    # Updates: valid_from and valid_to
    # Returns: updated promo code with status flags
```

---

## 📁 File 7: routes.py (Backend)

### Change 1: Updated Imports
```python
# BEFORE
from .repository import (
    create_promo_code,
    delete_promo_code,
    get_promo_codes,
    update_promo_code_status,
)

# AFTER
from .repository import (
    create_promo_code,
    delete_promo_code,
    get_promo_codes,
    update_promo_code_status,
    update_promo_code,
)
```

### Change 2: Added Update Endpoint
```python
# ADDED
@router.patch("/{promo_code_id}", response_model=ResponseModal)
async def edit_promo_code(
    promo_code_id: int = Path(...),
    promo_code_data: PromoCodeCreate = None,
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """Update promo code dates"""
    return await update_promo_code(db, promo_code_id, promo_code_data)
```

---

## 🎯 Summary of Changes

| File | Changes | Type |
|------|---------|------|
| PromoCode.tsx | 6 changes | Frontend |
| CreatePromoCodeModal.tsx | 7 changes | Frontend |
| promoCodeService.ts | 1 change | Frontend |
| promoCodeInterface.ts | 2 changes | Frontend |
| translation.json | 4 keys | Frontend |
| repository.py | 1 function | Backend |
| routes.py | 2 changes | Backend |

**Total: 7 files modified, 23 changes made**

---

## ✅ All Changes Complete

All changes have been implemented and tested for syntax errors. Ready for functional testing!

