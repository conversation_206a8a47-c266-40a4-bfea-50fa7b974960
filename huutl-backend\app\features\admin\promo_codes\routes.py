"""Routes for promo codes in the admin feature."""

from typing import Optional, Dict, Any

from fastapi import APIRouter, Depends, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config.database import get_db
from app.core.schema import ResponseModal
from app.middlewares.verify_token import verify_token

from .repository import (
    create_promo_code,
    delete_promo_code,
    get_promo_codes,
    update_promo_code_status,
    update_promo_code,
)
from .schema import PromoCodeCreate, PromoCodeUpdate, PromoCodeUpdateDates

router = APIRouter(prefix="/promo-codes", tags=["Promo Codes"])

db_dependency = Depends(get_db)


@router.get("", response_model=ResponseModal)
async def get_all_promo_codes(
    skip: int = 0,
    limit: int = 10,
    search: Optional[str] = None,
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """
    Get all promo codes with optional search and pagination.

    Args:
        skip: Number of promo codes to skip
        limit: Number of promo codes to return
        search: Search query for promo code names
        db: Database session

    Returns:
        Response with list of promo codes or error message
    """
    return await get_promo_codes(db, skip, limit, search)


@router.post("", response_model=ResponseModal)
async def create_new_promo_code(
    promo_code_data: PromoCodeCreate,
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """
    Create a new promo code.

    Args:
        promo_code_data: Promo code data for creation
        db: Database session

    Returns:
        Response with created promo code data or error message
    """
    return await create_promo_code(db, promo_code_data)


@router.delete("/{promo_code_id}", response_model=ResponseModal)
async def remove_promo_code(
    promo_code_id: int = Path(..., description="ID of the promo code to delete"),
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """
    Delete a promo code by ID.

    Args:
        promo_code_id: ID of the promo code to delete
        db: Database session

    Returns:
        Response with success message or error message
    """
    return await delete_promo_code(db, promo_code_id)


@router.patch("/status", response_model=ResponseModal)
async def change_promo_code_status(
    promo_code_data: PromoCodeUpdate,
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """
    Update promo code status (activate/deactivate).

    Args:
        promo_code_data: Promo code update data containing ID and new status
        db: Database session

    Returns:
        Response with updated promo code data or error message
    """
    return await update_promo_code_status(db, promo_code_data)


@router.patch("/{promo_code_id}", response_model=ResponseModal)
async def edit_promo_code(
    promo_code_id: int = Path(..., description="ID of the promo code to update"),
    promo_code_data: PromoCodeUpdateDates,
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """
    Update promo code dates (valid_from and valid_until).
    Only allows updating dates when promo code is active and not expired.

    Args:
        promo_code_id: ID of the promo code to update
        promo_code_data: Promo code data with new dates
        db: Database session

    Returns:
        Response with updated promo code data or error message
    """
    return await update_promo_code(db, promo_code_id, promo_code_data)
