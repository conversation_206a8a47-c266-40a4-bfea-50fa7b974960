# Backend Validation Implementation Guide

## Overview

This guide provides step-by-step instructions to add backend validation for promo code status updates.

---

## Current Implementation

**File**: `huutl-backend/app/features/admin/promo_codes/repository.py`

**Current Code** (Lines 215-281):
```python
async def update_promo_code_status(
    db: AsyncSession, promo_code_data: PromoCodeUpdate
) -> ResponseModal:
    """Update promo code status (activate/deactivate)."""
    try:
        # Fetch promo code
        result = db.execute(
            select(PromoCode).filter(PromoCode.id == promo_code_data.id)
        )
        promo_code = result.scalar_one_or_none()

        if not promo_code:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # ❌ MISSING: Expiration check
        # ❌ MISSING: Status transition validation
        
        # Update the status
        promo_code.status = PromoCodeStatus(promo_code_data.status)
        db.commit()
        db.refresh(promo_code)
        
        # ... response preparation ...
```

---

## Implementation Steps

### Step 1: Add Expiration Check

**Location**: After promo code is found, before status update

```python
# Check if promo code is expired
if _is_expired(promo_code):
    return ResponseModal(
        success=False,
        data=None,
        message=RESPONSE_MESSAGES.get(
            "PROMO_CODE_EXPIRED",
            "Cannot change status of an expired promo code"
        ),
        error=None,
        status_code=status.HTTP_400_BAD_REQUEST,
    )
```

### Step 2: Add Status Transition Validation (Optional)

**Location**: After expiration check

```python
# Validate status transition
current_status = promo_code.status.value
new_status = promo_code_data.status

# Prevent reactivating expired codes
if current_status == "expired" and new_status == "active":
    return ResponseModal(
        success=False,
        data=None,
        message=RESPONSE_MESSAGES.get(
            "CANNOT_REACTIVATE_EXPIRED",
            "Cannot reactivate an expired promo code"
        ),
        error=None,
        status_code=status.HTTP_400_BAD_REQUEST,
    )
```

### Step 3: Add Response Messages

**File**: `huutl-backend/app/core/utils/constants.py`

**Add these messages**:
```python
RESPONSE_MESSAGES = {
    # ... existing messages ...
    "PROMO_CODE_EXPIRED": "Cannot change status of an expired promo code",
    "CANNOT_REACTIVATE_EXPIRED": "Cannot reactivate an expired promo code",
    "INVALID_STATUS_TRANSITION": "Invalid status transition",
}
```

---

## Complete Updated Function

```python
async def update_promo_code_status(
    db: AsyncSession, promo_code_data: PromoCodeUpdate
) -> ResponseModal:
    """
    Update promo code status (activate/deactivate).
    
    Validates:
    1. Promo code exists
    2. Promo code is not expired
    3. Status transition is valid
    
    Args:
        db: Database session
        promo_code_data: Promo code update data
        
    Returns:
        Response with updated promo code data or error message
    """
    try:
        # Fetch promo code
        result = db.execute(
            select(PromoCode).filter(PromoCode.id == promo_code_data.id)
        )
        promo_code = result.scalar_one_or_none()

        # Validation 1: Promo code exists
        if not promo_code:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Validation 2: Promo code is not expired
        if _is_expired(promo_code):
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_EXPIRED"],
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Validation 3: Valid status transition
        current_status = promo_code.status.value
        new_status = promo_code_data.status
        
        if current_status == "expired" and new_status == "active":
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["CANNOT_REACTIVATE_EXPIRED"],
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Update the status
        promo_code.status = PromoCodeStatus(new_status)
        db.commit()
        db.refresh(promo_code)

        # Prepare response data with status flags
        is_expired = _is_expired(promo_code)
        response_data = {
            "id": promo_code.id,
            "name": promo_code.name,
            "discount_value": promo_code.discount,
            "valid_from": promo_code.valid_from,
            "valid_until": promo_code.valid_to,
            "status": promo_code.status.value,
            "expired": is_expired,
            "inactive": promo_code.status == PromoCodeStatus.inactive,
            "active": promo_code.status == PromoCodeStatus.active and not is_expired,
            "created_at": promo_code.created_at,
            "updated_at": promo_code.updated_at,
        }

        return ResponseModal(
            success=True,
            data=response_data,
            message=RESPONSE_MESSAGES["PROMO_CODE_STATUS_UPDATED_SUCCESSFULLY"],
            error=None,
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        db.rollback()
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
```

---

## Testing the Implementation

### Test Case 1: Expired Promo Code
```bash
# Request
PATCH /promo-codes/status
{
    "id": 1,
    "status": "active"
}

# Expected Response (if promo code is expired)
{
    "success": false,
    "message": "Cannot change status of an expired promo code",
    "status_code": 400
}
```

### Test Case 2: Valid Status Change
```bash
# Request
PATCH /promo-codes/status
{
    "id": 1,
    "status": "inactive"
}

# Expected Response (if promo code is valid)
{
    "success": true,
    "data": { ... },
    "message": "Promo code status updated successfully",
    "status_code": 200
}
```

### Test Case 3: Non-existent Promo Code
```bash
# Request
PATCH /promo-codes/status
{
    "id": 999,
    "status": "active"
}

# Expected Response
{
    "success": false,
    "message": "Promo code not found",
    "status_code": 404
}
```

---

## Frontend Integration

The frontend already handles these errors gracefully:

```typescript
if (response.success) {
    toastMessageSuccess(response.message);
    // Update UI
} else {
    toastMessageError(response.message);  // ← Shows backend error
}
```

Backend error messages will automatically display to users.

---

## Summary

✅ **Validation Checks Added**:
1. Promo code exists
2. Promo code is not expired
3. Status transition is valid

✅ **Security Improved**:
- Backend enforces business rules
- API is secure regardless of client
- Prevents invalid operations

✅ **User Experience**:
- Clear error messages from backend
- Frontend displays them to users
- Consistent behavior across all clients

