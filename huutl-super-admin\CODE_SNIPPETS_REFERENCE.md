# 💻 Code Snippets Reference

## 🎯 Key Implementation Code

### 1. Smart Routing Logic (PromoCode.tsx)

```typescript
// Handle status change button click
// If expired and action is ACTIVATE, open edit modal to update dates
// Otherwise, open status change confirmation modal
const handleStatusChangeClick = (
    promoCodeId: number,
    action: PromoCodeActionType
) => {
    const promo = promoCodesData.find((p) => p.id === promoCodeId);

    // If expired and trying to activate, open edit modal
    if (promo && promo.expired && action === PROMO_CODE_ACTION.ACTIVATE) {
        setEditPromoData(promo);
        setIsEditMode(true);
        setIsPromoModalOpen(true);
    } else {
        // Otherwise, open status change confirmation modal
        setSelectedPromoCodeId(promoCodeId);
        setStatusChangeAction(action);
        setIsStatusChangeModalOpen(true);
    }
};
```

---

### 2. Edit Submission Handler (PromoCode.tsx)

```typescript
// Handle promo code edit submission
const handlePromoCodeEditSubmit = async (data: IPromoCodeFormData) => {
    if (!editPromoData) return;

    try {
        setLoading((prev) => ({ ...prev, isSubmitting: true }));

        // Convert date strings to ISO datetime format for backend
        const validFromDate = new Date(data.validFrom + TIMEZONE_OFFSET);
        const validUntilDate = new Date(data.validUntil + TIMEZONE_OFFSET_END);

        const updateData = {
            valid_from: validFromDate.toISOString(),
            valid_until: validUntilDate.toISOString(),
        };

        const response = await updatePromoCodeService(editPromoData.id, updateData);

        if (response?.success) {
            toastMessageSuccess(response?.message);
            setIsPromoModalOpen(false);
            setIsEditMode(false);
            setEditPromoData(null);
            fetchPromoCodes(true); // Refresh the list
        } else {
            toastMessageError(response.message);
        }
    } catch (error) {
        console.error("Error updating promo code:", error);
        toastMessageError(t("something_went_wrong"));
    } finally {
        setLoading((prev) => ({ ...prev, isSubmitting: false }));
    }
};
```

---

### 3. Modal Usage (PromoCode.tsx)

```typescript
{/* Create/Edit Promo Code Modal */}
<CreateEditPromoCodeModal
    isOpen={isPromoModalOpen}
    onClose={() => {
        setIsPromoModalOpen(false);
        setIsEditMode(false);
        setEditPromoData(null);
    }}
    onSubmit={isEditMode ? handlePromoCodeEditSubmit : handlePromoCodeSubmit}
    isSubmitting={loading.isSubmitting}
    isEditMode={isEditMode}
    editData={editPromoData}
/>
```

---

### 4. Disabled Fields in Edit Mode (CreatePromoCodeModal.tsx)

```typescript
// Name field - disabled in edit mode
<Textbox
    control={control}
    name="name"
    label={t("promo_code_name")}
    placeholder={t("enter_promo_code_name")}
    disabled={isSubmitting || isEditMode}
    rules={{
        required: t("promo_code_name_required"),
        minLength: {
            value: 3,
            message: t("promo_code_name_min_length"),
        },
        maxLength: {
            value: 20,
            message: t("promo_code_name_max_length"),
        },
        pattern: {
            value: /^[a-zA-Z0-9]+$/,
            message: t("promo_code_name_valid"),
        },
    }}
/>

// Discount field - disabled in edit mode
<Textbox
    control={control}
    name="discountValue"
    label={t("discount_value")}
    placeholder={t("enter_discount_value")}
    type="number"
    disabled={isSubmitting || isEditMode}
    rules={{
        required: t("discount_value_required"),
        min: {
            value: 1,
            message: t("discount_value_min_1"),
        },
        max: {
            value: 100,
            message: t("discount_value_max_100"),
        },
    }}
/>

// Date fields - always editable
<Textbox
    control={control}
    name="validFrom"
    label={t("valid_from")}
    type="date"
    disabled={isSubmitting}
    min={minDate}
    rules={{
        required: t("valid_from_required"),
        validate: (value) => {
            const selectedDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return selectedDate >= today || t("valid_from_cannot_be_past_date");
        },
    }}
/>

<Textbox
    control={control}
    name="validUntil"
    label={t("valid_until")}
    type="date"
    disabled={isSubmitting}
    min={minDate}
    rules={{
        required: t("valid_until_required"),
        validate: (value) => {
            const selectedDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return selectedDate >= today || t("valid_until_cannot_be_past_date");
        },
    }}
/>
```

---

### 5. Update Service (promoCodeService.ts)

```typescript
export const updatePromoCodeService = async (
    promoCodeId: number,
    data: { valid_from: string; valid_until: string }
): Promise<ApiResponse> => {
    return await patch(
        `${endpoint.promoCodes.UPDATE_PROMO_CODE_STATUS.replace("/status", "")}/${promoCodeId}`,
        data
    );
};
```

---

### 6. Backend Update Function (repository.py)

```python
async def update_promo_code(
    db: AsyncSession, promo_code_id: int, promo_code_data: PromoCodeCreate
) -> ResponseModal:
    """
    Update promo code dates (valid_from and valid_until).
    Only allows updating dates when promo code is active and not expired.
    """
    try:
        # Check if promo code exists
        result = db.execute(
            select(PromoCode).filter(PromoCode.id == promo_code_id)
        )
        promo_code = result.scalar_one_or_none()

        if not promo_code:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Check if promo code is expired
        if _is_expired(promo_code):
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_EXPIRED"],
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Check if promo code is active
        if promo_code.status != PromoCodeStatus.active:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES.get(
                    "PROMO_CODE_NOT_ACTIVE",
                    "Promo code must be active to edit dates"
                ),
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Update the dates
        promo_code.valid_from = promo_code_data.valid_from
        promo_code.valid_to = promo_code_data.valid_until
        db.commit()
        db.refresh(promo_code)

        # Prepare response data with status flags
        is_expired = _is_expired(promo_code)
        response_data = {
            "id": promo_code.id,
            "name": promo_code.name,
            "discount_value": promo_code.discount,
            "valid_from": promo_code.valid_from,
            "valid_until": promo_code.valid_to,
            "status": promo_code.status.value,
            "expired": is_expired,
            "inactive": promo_code.status == PromoCodeStatus.inactive,
            "active": promo_code.status == PromoCodeStatus.active and not is_expired,
            "created_at": promo_code.created_at,
            "updated_at": promo_code.updated_at,
        }

        return ResponseModal(
            success=True,
            data=response_data,
            message=RESPONSE_MESSAGES.get(
                "PROMO_CODE_UPDATED_SUCCESSFULLY",
                "Promo code updated successfully"
            ),
            error=None,
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        db.rollback()
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
```

---

### 7. Backend Route (routes.py)

```python
@router.patch("/{promo_code_id}", response_model=ResponseModal)
async def edit_promo_code(
    promo_code_id: int = Path(..., description="ID of the promo code to update"),
    promo_code_data: PromoCodeCreate = None,
    db: AsyncSession = db_dependency,
    _: Dict[str, Any] = Depends(verify_token),
):
    """
    Update promo code dates (valid_from and valid_until).
    Only allows updating dates when promo code is active and not expired.
    """
    return await update_promo_code(db, promo_code_id, promo_code_data)
```

---

### 8. State Management (PromoCode.tsx)

```typescript
// Add to state
const [isEditMode, setIsEditMode] = useState(false);
const [editPromoData, setEditPromoData] = useState<IPromoCode | null>(null);
```

---

### 9. Translation Keys (translation.json)

```json
{
    "edit_promo_code": "Edit Promo Code",
    "edit_promo_code_dates_description": "Edit the valid from and valid until dates for this promo code",
    "update_promo_code": "Update Promo Code",
    "edit": "Edit"
}
```

---

## 🔑 Key Points

✅ **Smart Routing**: Check `promo.expired && action === ACTIVATE`
✅ **Field Disabling**: `disabled={isSubmitting || isEditMode}`
✅ **Date Conversion**: Use `TIMEZONE_OFFSET` for proper date handling
✅ **Backend Validation**: Multiple checks before updating
✅ **State Cleanup**: Reset state when modal closes
✅ **List Refresh**: Call `fetchPromoCodes(true)` after update

---

## 📝 Testing Code

```typescript
// Test: Create expired promo code
const testPromo = {
    name: "TEST20",
    discount_value: 20,
    valid_from: "2024-10-01T00:00:00Z",
    valid_until: "2024-10-15T23:59:59Z"
};

// Test: Click activate on expired promo
// Expected: Edit Modal opens
// Verify: Name and discount disabled, dates editable

// Test: Update dates
const updateData = {
    valid_from: "2024-11-01T00:00:00Z",
    valid_until: "2024-12-31T23:59:59Z"
};

// Test: Submit update
// Expected: Backend validates and updates
// Verify: List refreshes with new dates
```

