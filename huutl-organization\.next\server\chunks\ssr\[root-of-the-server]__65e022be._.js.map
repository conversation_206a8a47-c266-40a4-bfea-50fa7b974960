{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/validations/authValidations.ts"], "sourcesContent": ["import {\r\n\tEMAIL_REGEX,\r\n\tORG_NAME_ALLOWED_ONLY_CHARACTERS,\r\n\tPASSWORD_REGEX,\r\n\tUS_PHONE_NUMBER_VALIDATION,\r\n} from \"@/constants/commonConstants\";\r\nimport * as yup from \"yup\";\r\n\r\n/**\r\n * Login form validation schema\r\n * @param translate - Translation function for error messages\r\n * @returns Yup validation schema\r\n */\r\nexport const loginValidation = (translate: (key: string) => string) => {\r\n\treturn yup.object().shape({\r\n\t\temail: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translate(\"email_is_required\"))\r\n\t\t\t.matches(EMAIL_REGEX, translate(\"email_val_msg\"))\r\n\t\t\t.email(translate(\"please_enter_valid_email_address\")),\r\n\t\tpassword: yup.string().required(translate(\"password_is_required\")),\r\n\t\trememberMe: yup.boolean().optional(),\r\n\t});\r\n};\r\n\r\n/**\r\n * Registration form validation schema\r\n * @param translate - Translation function for error messages\r\n * @returns Yup validation schema\r\n */\r\nexport const signUpValidation = (translate: (key: string) => string) => {\r\n\treturn yup.object().shape({\r\n\t\tname: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translate(\"name_is_required\"))\r\n\t\t\t.min(2, translate(\"name_must_be_at_least_2_characters_long\"))\r\n\t\t\t.max(25, translate(\"name_must_be_at_most_25_characters_long\")),\r\n\t\torganizationName: yup\r\n\t\t\t.string()\r\n\t\t\t.trim()\r\n\t\t\t.required(translate(\"org_name_req\"))\r\n\t\t\t.matches(ORG_NAME_ALLOWED_ONLY_CHARACTERS, translate(\"org_name_alpha\"))\r\n\t\t\t.min(3, translate(\"org_name_min\"))\r\n\t\t\t.max(50, translate(\"org_name_max\")),\r\n\t\temail: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translate(\"email_is_required\"))\r\n\t\t\t.email(translate(\"please_enter_valid_email_address\"))\r\n\t\t\t.matches(EMAIL_REGEX, translate(\"email_val_msg\")),\r\n\t\tphoneNumber: yup\r\n\t\t\t.string()\r\n\t\t\t.trim()\r\n\t\t\t.required(translate(\"phone_req\"))\r\n\t\t\t.matches(US_PHONE_NUMBER_VALIDATION, translate(\"phone_format\")),\r\n\t\tpassword: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translate(\"password_is_required\"))\r\n\t\t\t.matches(PASSWORD_REGEX, translate(\"password_check_regex\")),\r\n\t\tconfirmPassword: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translate(\"confirm_password_is_required\"))\r\n\t\t\t.oneOf([yup.ref(\"password\")], translate(\"passwords_must_match\")),\r\n\t});\r\n};\r\n\r\n/**\r\n * Forgot password form validation schema\r\n * @param translate - Translation function for error messages\r\n * @returns Yup validation schema\r\n */\r\nexport const forgotPasswordValidation = (\r\n\ttranslate: (key: string) => string\r\n) => {\r\n\treturn yup.object().shape({\r\n\t\temail: yup\r\n\t\t\t.string()\r\n\t\t\t.trim()\r\n\t\t\t.required(translate(\"email_is_required\"))\r\n\t\t\t.email(translate(\"email_val_msg\"))\r\n\t\t\t.matches(EMAIL_REGEX, translate(\"email_val_msg\")),\r\n\t});\r\n};\r\n\r\nexport const verifyOTPValidation = (translation: (key: string) => string) =>\r\n\tyup.object().shape({\r\n\t\totp: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translation(\"otp_req\"))\r\n\t\t\t.length(6, translation(\"verification_code_length_invalid\"))\r\n\t\t\t.matches(/^[0-9]+$/, translation(\"verification_code_numeric_only\")),\r\n\t});\r\n\r\nexport const resetPasswordValidation = (translation: (key: string) => string) =>\r\n\tyup.object().shape({\r\n\t\tnew_password: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translation(\"password_is_required\"))\r\n\t\t\t.matches(PASSWORD_REGEX, translation(\"password_check_regex\")),\r\n\t\tconfirm_password: yup\r\n\t\t\t.string()\r\n\t\t\t.required(translation(\"confirm_password_is_required\"))\r\n\t\t\t.oneOf([yup.ref(\"new_password\")], translation(\"passwords_must_match\")),\r\n\t});\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAMA;;;AAOO,MAAM,kBAAkB,CAAC;IAC/B,OAAO,6IAAU,GAAG,KAAK,CAAC;QACzB,OAAO,6IACC,GACN,QAAQ,CAAC,UAAU,sBACnB,OAAO,CAAC,kJAAW,EAAE,UAAU,kBAC/B,KAAK,CAAC,UAAU;QAClB,UAAU,6IAAU,GAAG,QAAQ,CAAC,UAAU;QAC1C,YAAY,8IAAW,GAAG,QAAQ;IACnC;AACD;AAOO,MAAM,mBAAmB,CAAC;IAChC,OAAO,6IAAU,GAAG,KAAK,CAAC;QACzB,MAAM,6IACE,GACN,QAAQ,CAAC,UAAU,qBACnB,GAAG,CAAC,GAAG,UAAU,4CACjB,GAAG,CAAC,IAAI,UAAU;QACpB,kBAAkB,6IACV,GACN,IAAI,GACJ,QAAQ,CAAC,UAAU,iBACnB,OAAO,CAAC,uKAAgC,EAAE,UAAU,mBACpD,GAAG,CAAC,GAAG,UAAU,iBACjB,GAAG,CAAC,IAAI,UAAU;QACpB,OAAO,6IACC,GACN,QAAQ,CAAC,UAAU,sBACnB,KAAK,CAAC,UAAU,qCAChB,OAAO,CAAC,kJAAW,EAAE,UAAU;QACjC,aAAa,6IACL,GACN,IAAI,GACJ,QAAQ,CAAC,UAAU,cACnB,OAAO,CAAC,iKAA0B,EAAE,UAAU;QAChD,UAAU,6IACF,GACN,QAAQ,CAAC,UAAU,yBACnB,OAAO,CAAC,qJAAc,EAAE,UAAU;QACpC,iBAAiB,6IACT,GACN,QAAQ,CAAC,UAAU,iCACnB,KAAK,CAAC;YAAC,0IAAO,CAAC;SAAY,EAAE,UAAU;IAC1C;AACD;AAOO,MAAM,2BAA2B,CACvC;IAEA,OAAO,6IAAU,GAAG,KAAK,CAAC;QACzB,OAAO,6IACC,GACN,IAAI,GACJ,QAAQ,CAAC,UAAU,sBACnB,KAAK,CAAC,UAAU,kBAChB,OAAO,CAAC,kJAAW,EAAE,UAAU;IAClC;AACD;AAEO,MAAM,sBAAsB,CAAC,cACnC,6IAAU,GAAG,KAAK,CAAC;QAClB,KAAK,6IACG,GACN,QAAQ,CAAC,YAAY,YACrB,MAAM,CAAC,GAAG,YAAY,qCACtB,OAAO,CAAC,YAAY,YAAY;IACnC;AAEM,MAAM,0BAA0B,CAAC,cACvC,6IAAU,GAAG,KAAK,CAAC;QAClB,cAAc,6IACN,GACN,QAAQ,CAAC,YAAY,yBACrB,OAAO,CAAC,qJAAc,EAAE,YAAY;QACtC,kBAAkB,6IACV,GACN,QAAQ,CAAC,YAAY,iCACrB,KAAK,CAAC;YAAC,0IAAO,CAAC;SAAgB,EAAE,YAAY;IAChD", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/assets/images/logo.png.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 405, height: 105, blurWidth: 8, blurHeight: 2, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ANXC9PPFsuXkS0pOS1NTU08wMDAtODg4NE9PT0llZWVfAKabt7SzqMXCPz5APWVlZV95eXlxeXl5cXl5eXFpaWll4T8c3LRr/PMAAAAASUVORK5CYII=\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,mIAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,WAAW;IAAG,YAAY;IAAG,aAAa;AAA4M", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/assets/images/auth-after-right.png.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1002, height: 1419, blurWidth: 6, blurHeight: 8, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAAICAYAAADaxo44AAAAP0lEQVR42p2LSQ4AIAgDW+ry/x+LBpSzJA3JTAt8HyEYOoyD+18oB+K0CI/wRkJlvM1sb9AyIWYV/Qi8hRWpBVhoAWfQ936AAAAAAElFTkSuQmCC\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,qJAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAAwL", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/assets/images/auth-before-left.png.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 695, height: 1682, blurWidth: 3, blurHeight: 8, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAICAYAAAA870V8AAAANElEQVR42l2MUQ4AIAhCBVt1/xMHtloLvx4CEa+gu0p0IPLA4DZQ0GzU94dUTEDDJNXx7gIY0QCi+YjyKgAAAABJRU5ErkJggg==\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,qJAAG;IAAE,OAAO;IAAK,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAA4K", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/assets/images/eye-close.png.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 43, height: 30, blurWidth: 8, blurHeight: 6, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAAy0lEQVR42hWNvwqCUByFb4OQQ4MgNEggTeIgmE4NIoKmqCSK4OYVDOTeJwgaau/P1NAcBT1B7fYCPkDv0BgN0e8e+JbvwDlIFMVRGIZ7wzDmZVnegdZ13R34cQ+CVFWN67p+JUly0TRtZts2xRh3RVG0kiRNECsppV8Q5yiKjpZl4TiOD+B+eZ4/UJqmV0LIh8ksy26O46yBZdM0b1jbIEEQhp7nHeC7g5tTVVXPIAi2pmnmPM8PEAvHcX1Zlqe6rhNg4fv+SlGUkHV/joM704SswAIAAAAASUVORK5CYII=\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,2IAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,WAAW;IAAG,YAAY;IAAG,aAAa;AAAoX", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/assets/images/eye-open.png.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 20, height: 15, blurWidth: 8, blurHeight: 6, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAGCAYAAAD+Bd/7AAAAuklEQVR42hWOPQuCUBhGL02hu2MNCi4J5QcoRChc7V4/rl7BpX5BBU0NDTVEFBRBYwr1X3t8pxfO4fCQAU7X9akQ4pXneccYa6WUnyAIKkVRhgRwAnjzPG8Zx/GGUnqwLIviv/i+vyKAd9M0567rNlEU7RzHEYAnTdNGnPOOlGX5hLBAoQnDcG/bdpUkyRnCOE3THzEMY1YUxQMFBrDNsuwImaNyxY416Uf2EkpvjPz22bquW0CpqurwD+VSMWCADQIiAAAAAElFTkSuQmCC\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,0IAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,WAAW;IAAG,YAAY;IAAG,aAAa;AAA4V", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/formElements/InputWrapper.tsx"], "sourcesContent": ["import type { <PERSON>actNode, JSX } from \"react\";\r\nimport Image, { StaticImageData } from \"next/image\";\r\n\r\n/**\r\n * Wrapper component for input fields\r\n * @param {string} className - Class name for the input field\r\n * @returns {JSX.Element} - Wrapper component\r\n */\r\nconst InputWrapper = ({\r\n\tclassName,\r\n\tchildren,\r\n}: {\r\n\tclassName?: string;\r\n\tchildren: ReactNode;\r\n}): JSX.Element => (\r\n\t<div className={`form-group ${className ?? \"\"}`}>{children}</div>\r\n);\r\n\r\n/**\r\n * Label component for input fields\r\n * @param {string} children - Label text\r\n * @returns {JSX.Element} - Label component\r\n */\r\n/* eslint-disable react/display-name */\r\nInputWrapper.Label = function ({\r\n\tchildren,\r\n\thtmlFor,\r\n\toptional,\r\n\tclassName,\r\n}: {\r\n\tchildren: ReactNode;\r\n\thtmlFor?: string;\r\n\toptional?: boolean;\r\n\tclassName?: string;\r\n}): JSX.Element {\r\n\treturn (\r\n\t\t<label htmlFor={htmlFor} className={className}>\r\n\t\t\t{children}\r\n\t\t\t{optional ? <span className=\"text-muted\"> (Optional)</span> : null}\r\n\t\t</label>\r\n\t);\r\n};\r\n\r\n/**\r\n * Error component for input fields to display error message\r\n * @param { string } message - Error message\r\n * @returns { JSX.Element } - Error component\r\n */\r\nInputWrapper.Error = function ({\r\n\tmessage,\r\n}: {\r\n\tmessage: string;\r\n}): JSX.Element | null {\r\n\treturn message ? <p className=\"auth-msg-error\">{message}</p> : null;\r\n};\r\n\r\n/**\r\n * Icon component for input fields\r\n * @param { string } src - Icon source\r\n * @param { function } onClick - Function to be called on click\r\n * @returns { JSX.Element } - Icon component\r\n */\r\nInputWrapper.Icon = function ({\r\n\tsrc,\r\n\tonClick,\r\n\tariaLabel = \"icon button\",\r\n}: {\r\n\tsrc: string | StaticImageData;\r\n\tonClick?: () => void;\r\n\tariaLabel?: string;\r\n}): JSX.Element {\r\n\treturn (\r\n\t\t<button\r\n\t\t\tclassName=\"show-icon\"\r\n\t\t\ttype=\"button\"\r\n\t\t\tonClick={onClick}\r\n\t\t\taria-label={ariaLabel}\r\n\t\t>\r\n\t\t\t<Image src={src} alt=\"icon\" />\r\n\t\t</button>\r\n\t);\r\n};\r\n\r\nexport default InputWrapper;\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEA;;;;CAIC,GACD,MAAM,eAAe,CAAC,EACrB,SAAS,EACT,QAAQ,EAIR,iBACA,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,aAAa,IAAI;kBAAG;;;;;;AAGnD;;;;CAIC,GACD,qCAAqC,GACrC,aAAa,KAAK,GAAG,SAAU,EAC9B,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EAMT;IACA,qBACC,8OAAC;QAAM,SAAS;QAAS,WAAW;;YAClC;YACA,yBAAW,8OAAC;gBAAK,WAAU;0BAAa;;;;;uBAAqB;;;;;;;AAGjE;AAEA;;;;CAIC,GACD,aAAa,KAAK,GAAG,SAAU,EAC9B,OAAO,EAGP;IACA,OAAO,wBAAU,8OAAC;QAAE,WAAU;kBAAkB;;;;;eAAe;AAChE;AAEA;;;;;CAKC,GACD,aAAa,IAAI,GAAG,SAAU,EAC7B,GAAG,EACH,OAAO,EACP,YAAY,aAAa,EAKzB;IACA,qBACC,8OAAC;QACA,WAAU;QACV,MAAK;QACL,SAAS;QACT,cAAY;kBAEZ,cAAA,8OAAC,wIAAK;YAAC,KAAK;YAAK,KAAI;;;;;;;;;;;AAGxB;uCAEe", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/formElements/Textbox.tsx"], "sourcesContent": ["import React, { type InputHTMLAttributes } from \"react\";\r\nimport {\r\n\tController,\r\n\ttype Control,\r\n\ttype FieldValues,\r\n\ttype Path,\r\n\ttype PathValue,\r\n} from \"react-hook-form\";\r\n\r\n// Step 1: Define the props as generic\r\ninterface TextboxProps<T extends FieldValues>\r\n\textends Omit<InputHTMLAttributes<HTMLInputElement>, \"defaultValue\"> {\r\n\tname: Path<T>;\r\n\tcontrol: Control<T>;\r\n\talign?: \"left\" | \"right\";\r\n\tchildren?: React.ReactNode;\r\n\tdefaultValue?: PathValue<T, Path<T>>;\r\n}\r\n\r\n// Step 2: Define the generic component using React.forwardRef\r\nfunction TextboxInner<T extends FieldValues>(\r\n\t{ children, control, name, align, defaultValue, ...props }: TextboxProps<T>,\r\n\tref: React.Ref<HTMLInputElement>\r\n) {\r\n\treturn (\r\n\t\t<div className={`icon-align ${align ?? \"\"}`}>\r\n\t\t\t<Controller\r\n\t\t\t\tcontrol={control}\r\n\t\t\t\tname={name}\r\n\t\t\t\tdefaultValue={defaultValue}\r\n\t\t\t\trender={({ field }) => {\r\n\t\t\t\t\t// Ensure field.value is never undefined to prevent uncontrolled to controlled warning\r\n\t\t\t\t\tconst value = field.value === undefined ? \"\" : field.value;\r\n\t\t\t\t\treturn <input {...props} {...field} value={value} ref={ref} />;\r\n\t\t\t\t}}\r\n\t\t\t/>\r\n\t\t\t{children}\r\n\t\t</div>\r\n\t);\r\n}\r\n\r\n// Step 3: Export a forwardRef-wrapped generic component\r\nconst Textbox = React.forwardRef(TextboxInner) as <T extends FieldValues>(\r\n\tprops: TextboxProps<T> & { ref?: React.Ref<HTMLInputElement> }\r\n) => ReturnType<typeof TextboxInner>;\r\n\r\nexport default Textbox;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAkBA,8DAA8D;AAC9D,SAAS,aACR,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,OAAwB,EAC3E,GAAgC;IAEhC,qBACC,8OAAC;QAAI,WAAW,CAAC,WAAW,EAAE,SAAS,IAAI;;0BAC1C,8OAAC,4KAAU;gBACV,SAAS;gBACT,MAAM;gBACN,cAAc;gBACd,QAAQ,CAAC,EAAE,KAAK,EAAE;oBACjB,sFAAsF;oBACtF,MAAM,QAAQ,MAAM,KAAK,KAAK,YAAY,KAAK,MAAM,KAAK;oBAC1D,qBAAO,8OAAC;wBAAO,GAAG,KAAK;wBAAG,GAAG,KAAK;wBAAE,OAAO;wBAAO,KAAK;;;;;;gBACxD;;;;;;YAEA;;;;;;;AAGJ;AAEA,wDAAwD;AACxD,MAAM,wBAAU,gNAAK,CAAC,UAAU,CAAC;uCAIlB", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/formElements/CheckBox.tsx"], "sourcesContent": ["import React, { type HTMLAttributes } from \"react\";\r\nimport {\r\n\tController,\r\n\ttype Control,\r\n\ttype FieldValues,\r\n\ttype Path,\r\n} from \"react-hook-form\";\r\n\r\ninterface CheckBoxProps<T extends FieldValues>\r\n\textends HTMLAttributes<HTMLInputElement> {\r\n\tname: Path<T>;\r\n\tcontrol: Control<T>;\r\n\tchildren?: React.ReactNode;\r\n\tvalue?: boolean;\r\n\tdisabled?: boolean;\r\n}\r\n\r\nexport default function CheckBox<T extends FieldValues>({\r\n\tname,\r\n\tcontrol,\r\n\tchildren,\r\n\tdisabled,\r\n\t...props\r\n}: CheckBoxProps<T>) {\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Controller\r\n\t\t\t\trender={({ field }) => (\r\n\t\t\t\t\t<input\r\n\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t{...props}\r\n\t\t\t\t\t\t{...field}\r\n\t\t\t\t\t\tvalue={field.value ?? \"\"}\r\n\t\t\t\t\t\tchecked={field.value ?? false}\r\n\t\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\t\tonChange={(r) => {\r\n\t\t\t\t\t\t\tfield.onChange(r);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\tname={name}\r\n\t\t\t\tcontrol={control}\r\n\t\t\t\tdefaultValue={false as T[typeof name]}\r\n\t\t\t/>\r\n\t\t\t{children}\r\n\t\t</>\r\n\t);\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAgBe,SAAS,SAAgC,EACvD,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,GAAG,OACe;IAClB,qBACC;;0BACC,8OAAC,4KAAU;gBACV,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACjB,8OAAC;wBACA,MAAK;wBACJ,GAAG,KAAK;wBACR,GAAG,KAAK;wBACT,OAAO,MAAM,KAAK,IAAI;wBACtB,SAAS,MAAM,KAAK,IAAI;wBACxB,UAAU;wBACV,UAAU,CAAC;4BACV,MAAM,QAAQ,CAAC;wBAChB;;;;;;gBAGF,MAAM;gBACN,SAAS;gBACT,cAAc;;;;;;YAEd;;;AAGJ", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/utils/tostType.ts"], "sourcesContent": ["import toast from \"react-hot-toast\";\r\n\r\n/**\r\n * Toast style object\r\n */\r\nconst style = {\r\n\tfontSize: \"16px\",\r\n};\r\n\r\n/**\r\n * Toast success message\r\n * @param message - The message to display\r\n */\r\nexport const toastMessageSuccess = (message: string) => {\r\n\tdismissAllToasts();\r\n\ttoast.success(message, {\r\n\t\tstyle,\r\n\t});\r\n};\r\n\r\n/**\r\n * Toast success message with icon\r\n * @param message - The message to display\r\n * @param icon - The icon to display\r\n */\r\nexport const toastMessageWithIcon = (message: string, icon: string) => {\r\n\tdismissAllToasts();\r\n\ttoast.success(message, {\r\n\t\tstyle,\r\n\t\ticon,\r\n\t});\r\n};\r\n\r\n/**\r\n * Toast error message\r\n * @param message - The message to display\r\n */\r\nexport const toastMessageError = (message: string) => {\r\n\tdismissAllToasts();\r\n\ttoast.error(message, {\r\n\t\tstyle,\r\n\t});\r\n};\r\n\r\n/**\r\n * Dismiss all existing toast notifications\r\n */\r\nexport const dismissAllToasts = () => {\r\n\ttoast.dismiss();\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEA;;CAEC,GACD,MAAM,QAAQ;IACb,UAAU;AACX;AAMO,MAAM,sBAAsB,CAAC;IACnC;IACA,kKAAK,CAAC,OAAO,CAAC,SAAS;QACtB;IACD;AACD;AAOO,MAAM,uBAAuB,CAAC,SAAiB;IACrD;IACA,kKAAK,CAAC,OAAO,CAAC,SAAS;QACtB;QACA;IACD;AACD;AAMO,MAAM,oBAAoB,CAAC;IACjC;IACA,kKAAK,CAAC,KAAK,CAAC,SAAS;QACpB;IACD;AACD;AAKO,MAAM,mBAAmB;IAC/B,kKAAK,CAAC,OAAO;AACd", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/utils/storage.ts"], "sourcesContent": ["/**\r\n * Storage utility for localStorage operations with type safety and error handling.\r\n *\r\n * Provides a consistent interface for localStorage operations with:\r\n * - Type safety\r\n * - Error handling\r\n * - SSR compatibility\r\n * - JSON serialization/deserialization\r\n */\r\n\r\ninterface IStorage {\r\n\tset: (key: string, data: unknown) => void;\r\n\tget: (key: string) => unknown;\r\n\tremove: (key: string) => void;\r\n\tremoveAll: () => void;\r\n\tgetAllKeys: () => string[];\r\n}\r\n\r\n/**\r\n * Storage utility class for localStorage operations\r\n */\r\nclass StorageManager implements IStorage {\r\n\t/**\r\n\t * Check if localStorage is available (for SSR compatibility)\r\n\t */\r\n\tprivate isStorageAvailable(): boolean {\r\n\t\treturn typeof window !== \"undefined\" && Boolean(window.localStorage);\r\n\t}\r\n\r\n\t/**\r\n\t * Set data in localStorage with JSON serialization\r\n\t * @param key - Storage key\r\n\t * @param data - Data to store\r\n\t */\r\n\tset(key: string, data: unknown): void {\r\n\t\tif (!this.isStorageAvailable()) return;\r\n\r\n\t\ttry {\r\n\t\t\tlocalStorage.setItem(key, JSON.stringify(data));\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(`Failed to set localStorage item \"${key}\":`, error);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Get data from localStorage with JSON deserialization\r\n\t * @param key - Storage key\r\n\t * @returns Parsed data or null\r\n\t */\r\n\tget(key: string): unknown {\r\n\t\tif (!this.isStorageAvailable()) return null;\r\n\r\n\t\ttry {\r\n\t\t\tconst data = localStorage.getItem(key);\r\n\t\t\treturn data ? JSON.parse(data) : null;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(`Failed to get localStorage item \"${key}\":`, error);\r\n\t\t\treturn null;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Remove specific item from localStorage\r\n\t * @param key - Storage key to remove\r\n\t */\r\n\tremove(key: string): void {\r\n\t\tif (!this.isStorageAvailable()) return;\r\n\r\n\t\ttry {\r\n\t\t\tlocalStorage.removeItem(key);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(`Failed to remove localStorage item \"${key}\":`, error);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Clear all localStorage data\r\n\t */\r\n\tremoveAll(): void {\r\n\t\tif (!this.isStorageAvailable()) return;\r\n\r\n\t\ttry {\r\n\t\t\tlocalStorage.clear();\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Failed to clear localStorage:\", error);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Get all localStorage keys\r\n\t * @returns Array of all storage keys\r\n\t */\r\n\tgetAllKeys(): string[] {\r\n\t\tif (!this.isStorageAvailable()) return [];\r\n\r\n\t\ttry {\r\n\t\t\tconst keys: string[] = [];\r\n\t\t\tfor (let i = 0; i < localStorage.length; i++) {\r\n\t\t\t\tconst key = localStorage.key(i);\r\n\t\t\t\tif (key) {\r\n\t\t\t\t\tkeys.push(key);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn keys;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Failed to get localStorage keys:\", error);\r\n\t\t\treturn [];\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// Export singleton instance\r\nconst storage = new StorageManager();\r\nexport default storage;\r\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAUD;;CAEC,GACD,MAAM;IACL;;EAEC,GACD,AAAQ,qBAA8B;QACrC,OAAO,gBAAkB,eAAe,QAAQ,OAAO,YAAY;IACpE;IAEA;;;;EAIC,GACD,IAAI,GAAW,EAAE,IAAa,EAAQ;QACrC,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;QAEhC,IAAI;YACH,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC1C,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACD;IAEA;;;;EAIC,GACD,IAAI,GAAW,EAAW;QACzB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,OAAO;QAEvC,IAAI;YACH,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;QAClC,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC3D,OAAO;QACR;IACD;IAEA;;;EAGC,GACD,OAAO,GAAW,EAAQ;QACzB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;QAEhC,IAAI;YACH,aAAa,UAAU,CAAC;QACzB,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC/D;IACD;IAEA;;EAEC,GACD,YAAkB;QACjB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI;QAEhC,IAAI;YACH,aAAa,KAAK;QACnB,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,iCAAiC;QAChD;IACD;IAEA;;;EAGC,GACD,aAAuB;QACtB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,OAAO,EAAE;QAEzC,IAAI;YACH,MAAM,OAAiB,EAAE;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC7C,MAAM,MAAM,aAAa,GAAG,CAAC;gBAC7B,IAAI,KAAK;oBACR,KAAK,IAAI,CAAC;gBACX;YACD;YACA,OAAO;QACR,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,EAAE;QACV;IACD;AACD;AAEA,4BAA4B;AAC5B,MAAM,UAAU,IAAI;uCACL", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/utils/helper.ts"], "sourcesContent": ["import storage from \"./storage\";\r\nimport { StorageKeys } from \"../constants/commonConstants\";\r\nimport type { IRememberMeData } from \"../interfaces/authInterfaces\";\r\nimport CryptoJS from \"crypto-js\";\r\n\r\nconst encryptionKey =\r\n\tprocess.env.NEXT_PUBLIC_ENCRYPTION_KEY || \"default-fallback-key\";\r\n\r\nexport interface RememberMeData {\r\n\temail: string;\r\n\tpassword: string;\r\n}\r\n\r\n/**\r\n * Save remember me credentials\r\n * @param email - User email\r\n * @param password - User password (will be base64 encoded)\r\n * @param rememberMe - Remember me state\r\n */\r\nexport const saveRememberMeCredentials = (\r\n\temail: string,\r\n\tpassword: string,\r\n\trememberMe: boolean\r\n): void => {\r\n\tif (rememberMe) {\r\n\t\tconst credentials: RememberMeData = {\r\n\t\t\temail,\r\n\t\t\tpassword: btoa(password), // Base64 encode password\r\n\t\t};\r\n\t\tstorage.set(StorageKeys.REMEMBER_ME, credentials);\r\n\t} else {\r\n\t\tclearRememberMeCredentials();\r\n\t}\r\n};\r\n\r\n/**\r\n * Get remember me credentials from storage\r\n * @returns RememberMeData object or null if not found\r\n */\r\nexport const getRememberMeCredentials = (): IRememberMeData | null => {\r\n\tconst credentials = storage.get(\r\n\t\tStorageKeys.REMEMBER_ME\r\n\t) as unknown as IRememberMeData;\r\n\r\n\tif (credentials && credentials.email && credentials.password) {\r\n\t\ttry {\r\n\t\t\tconst password = atob(credentials.password); // Base64 decode password\r\n\t\t\treturn {\r\n\t\t\t\temail: credentials.email,\r\n\t\t\t\tpassword,\r\n\t\t\t};\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Failed to decode remember me password:\", error);\r\n\t\t\tclearRememberMeCredentials();\r\n\t\t\treturn null;\r\n\t\t}\r\n\t}\r\n\r\n\treturn null;\r\n};\r\n\r\n/**\r\n * Clear remember me credentials from storage\r\n */\r\nexport const clearRememberMeCredentials = (): void => {\r\n\tstorage.remove(StorageKeys.REMEMBER_ME);\r\n};\r\n\r\n/**\r\n * Check if remember me is enabled\r\n * @returns boolean indicating if remember me is active\r\n */\r\nexport const isRememberMeEnabled = (): boolean => {\r\n\tconst credentials = storage.get(\r\n\t\tStorageKeys.REMEMBER_ME\r\n\t) as unknown as RememberMeData;\r\n\treturn Boolean(credentials?.email && credentials?.password);\r\n};\r\n\r\nexport const encryptInfo = (info: string) => {\r\n\tif (encryptionKey) {\r\n\t\treturn CryptoJS.AES.encrypt(info, encryptionKey).toString();\r\n\t}\r\n\treturn \"\";\r\n};\r\n\r\nexport const decryptInfo = (info: string) => {\r\n\tif (encryptionKey) {\r\n\t\tconst bytes = CryptoJS.AES.decrypt(decodeURIComponent(info), encryptionKey);\r\n\t\treturn bytes.toString(CryptoJS.enc.Utf8);\r\n\t}\r\n\treturn \"\";\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,gBACL,QAAQ,GAAG,CAAC,0BAA0B,IAAI;AAapC,MAAM,4BAA4B,CACxC,OACA,UACA;IAEA,IAAI,YAAY;QACf,MAAM,cAA8B;YACnC;YACA,UAAU,KAAK;QAChB;QACA,kIAAO,CAAC,GAAG,CAAC,kJAAW,CAAC,WAAW,EAAE;IACtC,OAAO;QACN;IACD;AACD;AAMO,MAAM,2BAA2B;IACvC,MAAM,cAAc,kIAAO,CAAC,GAAG,CAC9B,kJAAW,CAAC,WAAW;IAGxB,IAAI,eAAe,YAAY,KAAK,IAAI,YAAY,QAAQ,EAAE;QAC7D,IAAI;YACH,MAAM,WAAW,KAAK,YAAY,QAAQ,GAAG,yBAAyB;YACtE,OAAO;gBACN,OAAO,YAAY,KAAK;gBACxB;YACD;QACD,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,0CAA0C;YACxD;YACA,OAAO;QACR;IACD;IAEA,OAAO;AACR;AAKO,MAAM,6BAA6B;IACzC,kIAAO,CAAC,MAAM,CAAC,kJAAW,CAAC,WAAW;AACvC;AAMO,MAAM,sBAAsB;IAClC,MAAM,cAAc,kIAAO,CAAC,GAAG,CAC9B,kJAAW,CAAC,WAAW;IAExB,OAAO,QAAQ,aAAa,SAAS,aAAa;AACnD;AAEO,MAAM,cAAc,CAAC;IAC3B,wCAAmB;QAClB,OAAO,gJAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,eAAe,QAAQ;IAC1D;;;AAED;AAEO,MAAM,cAAc,CAAC;IAC3B,wCAAmB;QAClB,MAAM,QAAQ,gJAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,mBAAmB,OAAO;QAC7D,OAAO,MAAM,QAAQ,CAAC,gJAAQ,CAAC,GAAG,CAAC,IAAI;IACxC;;;AAED", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/views/auth/Login.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useForm, Resolver } from \"react-hook-form\";\r\nimport { yupResolver } from \"@hookform/resolvers/yup\";\r\nimport { loginValidation } from \"@/validations/authValidations\";\r\n\r\nimport Image from \"next/image\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/pagination\";\r\nimport Logo from \"../../assets/images/logo.png\";\r\nimport AuthAfterRight from \"../../assets/images/auth-after-right.png\";\r\nimport AuthBeforeLeft from \"../../assets/images/auth-before-left.png\";\r\nimport EyeCloseIcon from \"../../assets/images/eye-close.png\";\r\nimport EyeOpenIcon from \"../../assets/images/eye-open.png\";\r\nimport Link from \"next/link\";\r\nimport InputWrapper from \"@/components/formElements/InputWrapper\";\r\nimport Textbox from \"@/components/formElements/Textbox\";\r\nimport CheckBox from \"@/components/formElements/CheckBox\";\r\nimport Button from \"@/components/formElements/Button\";\r\nimport { useTranslate } from \"@/utils/translationUtils\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { signIn } from \"next-auth/react\";\r\nimport { getSession } from \"next-auth/react\";\r\nimport { AppDispatch } from \"@/redux/store\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { setAuthData } from \"@/redux/slices/authSlice\";\r\nimport ROUTES from \"@/constants/routes\";\r\nimport { ILogin, LoginFormData } from \"@/interfaces/authInterfaces\";\r\nimport { ISession } from \"@/interfaces/commonInterfaces\";\r\nimport { toastMessageError, toastMessageSuccess } from \"@/utils/tostType\";\r\nimport { setAuthTokenCookie } from \"@/utils/cookieUtils\";\r\nimport {\r\n\tclearRememberMeCredentials,\r\n\tgetRememberMeCredentials,\r\n\tsaveRememberMeCredentials,\r\n} from \"@/utils/helper\";\r\nimport { ACCOUNT_TYPE } from \"@/constants/commonConstants\";\r\n\r\nconst Login: React.FC = () => {\r\n\tconst router = useRouter();\r\n\tconst translate = useTranslate();\r\n\r\n\tconst [loading, setLoading] = useState(false);\r\n\tconst [showPassword, setShowPassword] = useState(false);\r\n\tconst [accountType, setAccountType] = useState(ACCOUNT_TYPE.ADMIN);\r\n\r\n\tconst dispatch = useDispatch<AppDispatch>();\r\n\r\n\tconst {\r\n\t\tcontrol,\r\n\t\thandleSubmit,\r\n\t\treset,\r\n\t\tformState: { errors },\r\n\t} = useForm({\r\n\t\tresolver: yupResolver(\r\n\t\t\tloginValidation(translate)\r\n\t\t) as Resolver<LoginFormData>,\r\n\t\tmode: \"onChange\",\r\n\t\tdefaultValues: {\r\n\t\t\temail: \"\",\r\n\t\t\tpassword: \"\",\r\n\t\t\trememberMe: false,\r\n\t\t},\r\n\t});\r\n\r\n\t// Load remembered credentials on component mount\r\n\tuseEffect(() => {\r\n\t\tconst rememberedData = getRememberMeCredentials();\r\n\t\tif (rememberedData) {\r\n\t\t\treset({\r\n\t\t\t\temail: rememberedData.email,\r\n\t\t\t\tpassword: rememberedData.password,\r\n\t\t\t\trememberMe: true,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [reset]);\r\n\r\n\tconst onSubmit = async (data: ILogin) => {\r\n\t\ttry {\r\n\t\t\tsetLoading(true);\r\n\t\t\tconst response = await signIn(\"credentials\", {\r\n\t\t\t\tredirect: false,\r\n\t\t\t\temail: data?.email,\r\n\t\t\t\tpassword: data?.password,\r\n\t\t\t\tpanel: accountType,\r\n\t\t\t\tcallbackUrl: `${window.location.origin}`,\r\n\t\t\t});\r\n\t\t\tsetLoading(false);\r\n\t\t\tif (response?.ok) {\r\n\t\t\t\tconst session = (await getSession()) as unknown as ISession;\r\n\r\n\t\t\t\tif (session?.user?.success) {\r\n\t\t\t\t\tdispatch(setAuthData(session.user.data));\r\n\t\t\t\t\tsetAuthTokenCookie(session.user.data.token, data?.rememberMe);\r\n\t\t\t\t\t// Handle remember me functionality\r\n\t\t\t\t\tif (data?.rememberMe) {\r\n\t\t\t\t\t\tsaveRememberMeCredentials(data.email, data.password, true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Clear remembered credentials if remember me is unchecked\r\n\t\t\t\t\t\tclearRememberMeCredentials();\r\n\t\t\t\t\t}\r\n\t\t\t\t\ttoastMessageSuccess(translate(\"login_success\"));\r\n\t\t\t\t\trouter.replace(ROUTES.DASHBOARD);\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttoastMessageError(\r\n\t\t\t\t\t\ttranslate(\r\n\t\t\t\t\t\t\t(session?.user?.message as string) || \"something_went_wrong\"\r\n\t\t\t\t\t\t)\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconst errorMessage = response?.error;\r\n\t\t\t\ttoastMessageError(\r\n\t\t\t\t\terrorMessage ? errorMessage : translate(\"something_went_wrong\")\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\ttoastMessageError(translate(\"something_went_wrong\"));\r\n\t\t}\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div className=\"userauth-main\">\r\n\t\t\t<div className=\"container-fluid\">\r\n\t\t\t\t<div className=\"row\">\r\n\t\t\t\t\t<div className=\"col-md-6 col-lg-6\">\r\n\t\t\t\t\t\t<div className=\"auth-left\">\r\n\t\t\t\t\t\t\t<Image\r\n\t\t\t\t\t\t\t\tsrc={AuthBeforeLeft}\r\n\t\t\t\t\t\t\t\talt=\"auth-before-left\"\r\n\t\t\t\t\t\t\t\tclassName=\"before-image\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Image\r\n\t\t\t\t\t\t\t\tsrc={AuthAfterRight}\r\n\t\t\t\t\t\t\t\talt=\"auth-after-right\"\r\n\t\t\t\t\t\t\t\tclassName=\"after-image\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<div className=\"welcome-section\">\r\n\t\t\t\t\t\t\t\t<div className=\"logo\">\r\n\t\t\t\t\t\t\t\t\t<Image src={Logo} alt=\"logo\" />\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<h1>{translate(\"welcome_to_huutl\")}</h1>\r\n\t\t\t\t\t\t\t\t<p>{translate(\"ai_business_partner\")}</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"col-md-6 col-lg-6\">\r\n\t\t\t\t\t\t<div className=\"auth-right\">\r\n\t\t\t\t\t\t\t<div className=\"auth-heading\">\r\n\t\t\t\t\t\t\t\t<h2>{translate(\"hello_welcome\")}</h2>\r\n\t\t\t\t\t\t\t\t<p>{translate(\"sign_in_to_account\")}</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"auth-form\">\r\n\t\t\t\t\t\t\t\t<div className=\"auth-tab\">\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\ttype=\"submit\"\r\n\t\t\t\t\t\t\t\t\t\tclassName={\r\n\t\t\t\t\t\t\t\t\t\t\taccountType === ACCOUNT_TYPE.ADMIN\r\n\t\t\t\t\t\t\t\t\t\t\t\t? \"primary-btn radius-md w-100\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t: \"grey-outline-btn radius-md w-100\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => setAccountType(ACCOUNT_TYPE.ADMIN)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"admin\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\ttype=\"submit\"\r\n\t\t\t\t\t\t\t\t\t\tclassName={\r\n\t\t\t\t\t\t\t\t\t\t\taccountType === ACCOUNT_TYPE.STAFF\r\n\t\t\t\t\t\t\t\t\t\t\t\t? \"primary-btn radius-md w-100\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t: \"grey-outline-btn radius-md w-100\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => setAccountType(ACCOUNT_TYPE.STAFF)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"staff\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div className=\"form-section\">\r\n\t\t\t\t\t\t\t\t\t<form onSubmit={handleSubmit(onSubmit)}>\r\n\t\t\t\t\t\t\t\t\t\t<InputWrapper>\r\n\t\t\t\t\t\t\t\t\t\t\t<InputWrapper.Label htmlFor=\"email\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"email\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</InputWrapper.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t<Textbox\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"email\"\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrol={control}\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={loading}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"enter_your_email\")}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<InputWrapper.Error\r\n\t\t\t\t\t\t\t\t\t\t\t\tmessage={errors.email?.message || \"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</InputWrapper>\r\n\t\t\t\t\t\t\t\t\t\t<InputWrapper>\r\n\t\t\t\t\t\t\t\t\t\t\t<InputWrapper.Label htmlFor=\"passowrd\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"password\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</InputWrapper.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t<Textbox\r\n\t\t\t\t\t\t\t\t\t\t\t\tname=\"password\"\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype={showPassword ? \"text\" : \"password\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontrol={control}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"form-control\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"enter_your_password\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\talign=\"right\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisabled={loading}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<InputWrapper.Icon\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => setShowPassword(!showPassword)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={showPassword ? EyeOpenIcon : EyeCloseIcon}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t</Textbox>\r\n\t\t\t\t\t\t\t\t\t\t\t<InputWrapper.Error\r\n\t\t\t\t\t\t\t\t\t\t\t\tmessage={errors.password?.message || \"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</InputWrapper>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"form-checkbox-align\">\r\n\t\t\t\t\t\t\t\t\t\t\t<InputWrapper.Label\r\n\t\t\t\t\t\t\t\t\t\t\t\thtmlFor=\"rememberMe\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"custom-checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"remember_me\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<CheckBox\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tname=\"rememberMe\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcontrol={control}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisabled={loading}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span className=\"checkmark\"></span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</CheckBox>\r\n\t\t\t\t\t\t\t\t\t\t\t</InputWrapper.Label>\r\n\t\t\t\t\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\t\t\t\t\thref={\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tloading\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: `${ROUTES.FORGOT_PASSWORD}?type=${accountType}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"forgot-password-link\"\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"forgot_password\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Link>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div className=\"action-btn\">\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\ttype=\"submit\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"primary-btn radius-md w-100\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tloading={loading}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"sign_in\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</form>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className=\"form-links\">\r\n\t\t\t\t\t\t\t\t<p>\r\n\t\t\t\t\t\t\t\t\t{translate(\"don_t_have_an_account\")}{\" \"}\r\n\t\t\t\t\t\t\t\t\t<span onClick={() => router.push(ROUTES.SIGNUP)}>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"sign_up\")}\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default Login;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AAKA;AArCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,MAAM,QAAkB;IACvB,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,YAAY,IAAA,gJAAY;IAE9B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC,mJAAY,CAAC,KAAK;IAEjE,MAAM,WAAW,IAAA,wKAAW;IAE5B,MAAM,EACL,OAAO,EACP,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACrB,GAAG,IAAA,yKAAO,EAAC;QACX,UAAU,IAAA,6KAAW,EACpB,IAAA,wJAAe,EAAC;QAEjB,MAAM;QACN,eAAe;YACd,OAAO;YACP,UAAU;YACV,YAAY;QACb;IACD;IAEA,iDAAiD;IACjD,IAAA,kNAAS,EAAC;QACT,MAAM,iBAAiB,IAAA,kJAAwB;QAC/C,IAAI,gBAAgB;YACnB,MAAM;gBACL,OAAO,eAAe,KAAK;gBAC3B,UAAU,eAAe,QAAQ;gBACjC,YAAY;YACb;QACD;IACD,GAAG;QAAC;KAAM;IAEV,MAAM,WAAW,OAAO;QACvB,IAAI;YACH,WAAW;YACX,MAAM,WAAW,MAAM,IAAA,wJAAM,EAAC,eAAe;gBAC5C,UAAU;gBACV,OAAO,MAAM;gBACb,UAAU,MAAM;gBAChB,OAAO;gBACP,aAAa,GAAG,OAAO,QAAQ,CAAC,MAAM,EAAE;YACzC;YACA,WAAW;YACX,IAAI,UAAU,IAAI;gBACjB,MAAM,UAAW,MAAM,IAAA,4JAAU;gBAEjC,IAAI,SAAS,MAAM,SAAS;oBAC3B,SAAS,IAAA,kJAAW,EAAC,QAAQ,IAAI,CAAC,IAAI;oBACtC,IAAA,iJAAkB,EAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;oBAClD,mCAAmC;oBACnC,IAAI,MAAM,YAAY;wBACrB,IAAA,mJAAyB,EAAC,KAAK,KAAK,EAAE,KAAK,QAAQ,EAAE;oBACtD,OAAO;wBACN,2DAA2D;wBAC3D,IAAA,oJAA0B;oBAC3B;oBACA,IAAA,+IAAmB,EAAC,UAAU;oBAC9B,OAAO,OAAO,CAAC,qIAAM,CAAC,SAAS;gBAChC,OAAO;oBACN,IAAA,6IAAiB,EAChB,UACC,AAAC,SAAS,MAAM,WAAsB;gBAGzC;YACD,OAAO;gBACN,MAAM,eAAe,UAAU;gBAC/B,IAAA,6IAAiB,EAChB,eAAe,eAAe,UAAU;YAE1C;QACD,EAAE,OAAO,OAAO;YACf,IAAA,6IAAiB,EAAC,UAAU;QAC7B;IACD;IAEA,qBACC,8OAAC;QAAI,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,wIAAK;oCACL,KAAK,8VAAc;oCACnB,KAAI;oCACJ,WAAU;;;;;;8CAEX,8OAAC,wIAAK;oCACL,KAAK,8VAAc;oCACnB,KAAI;oCACJ,WAAU;;;;;;8CAEX,8OAAC;oCAAI,WAAU;;sDACd,8OAAC;4CAAI,WAAU;sDACd,cAAA,8OAAC,wIAAK;gDAAC,KAAK,0TAAI;gDAAE,KAAI;;;;;;;;;;;sDAEvB,8OAAC;sDAAI,UAAU;;;;;;sDACf,8OAAC;sDAAG,UAAU;;;;;;;;;;;;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACd,8OAAC;sDAAI,UAAU;;;;;;sDACf,8OAAC;sDAAG,UAAU;;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACd,8OAAC;4CAAI,WAAU;;8DACd,8OAAC,uJAAM;oDACN,MAAK;oDACL,WACC,gBAAgB,mJAAY,CAAC,KAAK,GAC/B,gCACA;oDAEJ,SAAS,IAAM,eAAe,mJAAY,CAAC,KAAK;8DAE/C,UAAU;;;;;;8DAEZ,8OAAC,uJAAM;oDACN,MAAK;oDACL,WACC,gBAAgB,mJAAY,CAAC,KAAK,GAC/B,gCACA;oDAEJ,SAAS,IAAM,eAAe,mJAAY,CAAC,KAAK;8DAE/C,UAAU;;;;;;;;;;;;sDAGb,8OAAC;4CAAI,WAAU;sDACd,cAAA,8OAAC;gDAAK,UAAU,aAAa;;kEAC5B,8OAAC,6JAAY;;0EACZ,8OAAC,6JAAY,CAAC,KAAK;gEAAC,SAAQ;0EAC1B,UAAU;;;;;;0EAEZ,8OAAC,wJAAO;gEACP,MAAK;gEACL,MAAK;gEACL,SAAS;gEACT,UAAU;gEACV,WAAU;gEACV,aAAa,UAAU;;;;;;0EAExB,8OAAC,6JAAY,CAAC,KAAK;gEAClB,SAAS,OAAO,KAAK,EAAE,WAAW;;;;;;;;;;;;kEAGpC,8OAAC,6JAAY;;0EACZ,8OAAC,6JAAY,CAAC,KAAK;gEAAC,SAAQ;0EAC1B,UAAU;;;;;;0EAEZ,8OAAC,wJAAO;gEACP,MAAK;gEACL,MAAM,eAAe,SAAS;gEAC9B,SAAS;gEACT,WAAU;gEACV,aAAa,UAAU;gEACvB,OAAM;gEACN,UAAU;0EAEV,cAAA,8OAAC,6JAAY,CAAC,IAAI;oEACjB,SAAS,IAAM,gBAAgB,CAAC;oEAChC,KAAK,eAAe,wUAAW,GAAG,0UAAY;;;;;;;;;;;0EAGhD,8OAAC,6JAAY,CAAC,KAAK;gEAClB,SAAS,OAAO,QAAQ,EAAE,WAAW;;;;;;;;;;;;kEAGvC,8OAAC;wDAAI,WAAU;;0EACd,8OAAC,6JAAY,CAAC,KAAK;gEAClB,SAAQ;gEACR,WAAU;;oEAET,UAAU;kFACX,8OAAC,yJAAQ;wEACR,MAAK;wEACL,SAAS;wEACT,UAAU;kFAEV,cAAA,8OAAC;4EAAK,WAAU;;;;;;;;;;;;;;;;;0EAGlB,8OAAC,uKAAI;gEACJ,MACC,UACG,KACA,GAAG,qIAAM,CAAC,eAAe,CAAC,MAAM,EAAE,aAAa;gEAEnD,WAAU;0EAET,UAAU;;;;;;;;;;;;kEAGb,8OAAC;wDAAI,WAAU;kEACd,cAAA,8OAAC,uJAAM;4DACN,MAAK;4DACL,WAAU;4DACV,SAAS;sEAER,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;8CACd,cAAA,8OAAC;;4CACC,UAAU;4CAA0B;0DACrC,8OAAC;gDAAK,SAAS,IAAM,OAAO,IAAI,CAAC,qIAAM,CAAC,MAAM;0DAC5C,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;uCAEe", "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/app/%5Blocale%5D/login/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport Login from \"@/views/auth/Login\";\r\nimport React from \"react\";\r\n\r\nconst page = () => {\r\n\treturn <Login />;\r\n};\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAIA,MAAM,OAAO;IACZ,qBAAO,8OAAC,yIAAK;;;;;AACd;uCAEe", "debugId": null}}]}