r"""This file contains constants used throughout the application."""
import re

# Response Messages Dictionary
RESPONSE_MESSAGES = {
    "SIGNUP_SUCCESS": "User registration successful",
    "AUTHENTICATION_FAILED": "Authentication failed",
    "INTERNAL_SERVER_ERROR": "Internal server error",
    "LOGIN_SUCCESS": "Login successfully",
    "SOMETHING_WENT_WRONG": "Something went wrong.",
    "INVALID_EMAIL_PASSWORD": "Invalid email or password",
    "USER_ACCOUNT_INACTIVE": "Your account is inactive. Please contact support for assistance.",
    "DATABASE_QUERY_ERROR": "Database query error.",
    "ACCOUNT_VERIFICATION_REQUIRED": "Account verification required.",
    "USER_NOT_FOUND": "User not found.",
    "USER_NOT_EXIST": "User does not exist.",
    "TOKEN_EXPIRED": "Token has expired. Please login again.",
    "USER_NOT_FOUND_TOKEN": "User not found. Token may be invalid.",
    "ACCOUNT_EMAIL_NOT_VERIFIED": "Account email not verified. Please complete verification.",
    # Auth/Password
    "EMAIL_NOT_REGISTERED": "If the email is registered, a verification code has been sent.",
    "OTP_SEND_ISSUE": "There was an issue sending the verification code. Please try again later.",
    "OTP_VERIFIED_SUCCESSFULLY": "OTP verified successfully.",
    "MFA_VERIFICATION_SUCCESS": "MFA verification successful.",
    "PASSWORD_RESET_SUCCESS": "Password reset successfully. You can now log in with your new password.",
    "GENERIC_PROCESSING_ERROR": "An error occurred processing your request. Please try again later.",
    "GENERIC_VERIFICATION_ERROR": "An error occurred verifying your code. Please try again.",
    "GENERIC_RESET_ERROR": "An error occurred resetting your password. Please try again.",
    "INVALID_EMAIL_OR_OTP": "Invalid email or verification code.",
    "INVALID_VERIFICATION_CODE": "Invalid verification code.",
    "INVALID_MFA_VERIFICATION_CODE": "Invalid MFA verification code.",
    "VERIFICATION_CODE_EXPIRED": "Verification code has expired. Please request a new one.",
    "MFA_VERIFICATION_CODE_EXPIRED": "MFA verification code has expired. Please request a new one.",
    "MAX_VERIFICATION_ATTEMPTS_REACHED": "Maximum verification attempts reached.",
    "INVALID_RESET_REQUEST": "Invalid reset request. Please complete verification again.",
    "INVALID_OR_EXPIRED_RESET_REQUEST": "The reset password link has expired. Please initiate the reset process again.",
    "AUTHENTICATION_ERROR": "Authentication error.",
    "NO_RESULTS_FOUND": "No results found.",
    "RESULTS_FOUND": "Results found.",
    "REGISTRATION_SUCCESS": "Registration successful. Please check your email for a verification code.",
    "INVALID_TOKEN": "Invalid token.",
    "ACCOUNT_VERIFIED_SUCCESS": "Account verified successfully.",
    "ACCOUNT_VERIFIED_SUCCESSFULLY": "Account verified successfully.",
    "VERIFICATION_CODE_SENT": "Verification code sent successfully. Please check your email.",
    "NEW_VERIFICATION_CODE_SENT": "A new verification code has been sent to your email.",
    "UNABLE_TO_VERIFY_ACCOUNT": "Unable to verify account. Please contact support.",
    "EMAIL_ALREDAY_EXIST": "Email already exist.",
    # Profile
    "USER_PROFILE_RETRIEVED": "User profile retrieved successfully.",
    "USER_PROFILE_RETRIEVED_SUCCESSFULLY": "User profile retrieved successfully.",
    "FAILED_UPLOAD_PROFILE_IMAGE": "Failed to upload profile image.",
    "INVALID_IMAGE_FORMAT": "Invalid image format. Must be a base64 encoded image.",
    "PROFILE_UPDATED_SUCCESSFULLY": "Profile updated successfully.",
    "PASSWORD_CHANGED_SUCCESSFULLY": "Password changed successfully.",
    "INCORRECT_OLD_PASSWORD": "Incorrect old password.",
    "NEW_PASSWORD_SAME_AS_OLD": "New password cannot be the same as old password.",
    "NEW_PASSWORD_CANNOT_BE_THE_SAME_AS_OLD_PASSWORD": "The new password cannot be the same as the current password.",
    "USER_PROFILE_NOT_FOUND": "User profile not found.",
    # Additional auth messages
    "ACCOUNT_INACTIVE": "Your account is inactive. Please contact support for assistance.",
    "ACCOUNT_VERIFICATION_FAILED": "Account verification failed. Please try again.",
    "EMAIL_ALREADY_VERIFIED": "Email is already verified.",
    "EMAIL_NOT_VERIFIED": "Email not verified. Please complete verification.",
    "EMAIL_SEND_FAILED": "Failed to send email. Please try again later.",
    "INVALID_CREDENTIALS": "Invalid credentials.",
    "PASSWORD_RESET_EMAIL_SENT": "Password reset email sent successfully.",
    "PASSWORD_RESET_FAILED": "Password reset failed. Please try again.",
    "PASSWORD_RESET_SUCCESSFUL": "Password reset successful. You can now log in with your new password.",
    "EMAIL_ALREADY_EXIST": "Email already exist.",
    # Admin
    "ADMIN_ACCESS_DENIED": "Access denied.",
    "ADMIN_PROFILE_UPDATED": "Admin profile updated successfully.",
    # Permission and Access Control
    "PERMISSION_DENIED": "User not allowed to access this feature.",
    "PERMISSION_CHECK_FAILED": "An error occurred while checking permissions.",
    "ROLE_PERMISSION_DENIED": "Access denied. Required roles: {roles}",
    "ROLE_CHECK_FAILED": "An error occurred while checking role permissions.",
    "INSTALLER_ACCESS_DENIED": "Not an installer user",
    "ADMIN_PRIVILEGES_REQUIRED": "Access denied. Admin privileges required.",
    "S3_CLIENT_INIT_ERROR": "Failed to initialize S3 client: {error}",
    # Organizations
    "ORGANIZATION_NOT_FOUND": "Organization not found",
    "ORGANIZATION_FETCHED_SUCCESSFULLY": "Organization fetched successfully",
    "ORGANIZATION_NAME_EXIST": "Organization name already exist",
    "ORGANIZATION_CREATED_SUCCESSFULLY": "Organization created successfully",
    "ORGANIZATION_UPDATED_SUCCESSFULLY": "Organization updated successfully",
    "ORGANIZATION_DELETED_SUCCESSFULLY": "Organization deleted successfully",
    # Subscriptions
    "SUBSCRIPTION_NOT_FOUND": "Subscription not found.",
    "SUBSCRIPTION_FETCHED_SUCCESSFULLY": "Subscription fetched successfully.",
    # redis
    "REDIS_CLIENT_NOT_INITIALIZED": "Redis client not initialized.",
    "PLAN_NOT_FOUND": "Plan not found.",
    "PLAN_FETCHED_SUCCESSFULLY": "Plan fetched successfully.",
    "PROFILE_RETRIEVED_SUCCESSFULLY": "Profile retrieved successfully",
    "NO_DATA_TO_UPDATE": "No data to update",
    "INVALID_OLD_PASSWORD": "Invalid old password",
    "PRESIGNED_URLS_GENERATED_SUCCESSFULLY": "Presigned URLs generated successfully",
    "NO_FILES_PROVIDED": "No files provided",
    "PROMO_CODE_NAME_ALREADY_EXISTS":"Promo code with this name already exists",
    "PROMO_CODE_CREATED_SUCCESSFULLY":"Promo code created successfully",
    "PROMO_CODE_NOT_FOUND":"No promo codes found",
    "PROMO_CODE_FETCHED_SUCCESSFULLY":"Promo codes fetched successfully",
    "PROMO_CODE_DELETED_SUCCESSFULLY":"Promo code deleted successfully",
    "PROMO_CODE_STATUS_UPDATED_SUCCESSFULLY":"Promo code status updated successfully",
    "PROMO_CODE_EXPIRED":"Promo code is expired",
}

# JWT Token constants
JWT_PAYLOAD_FIELDS = ["user_id", "email", "exp"]
USER_VERIFIED_STATUS = True
USER_UNVERIFIED_STATUS = False
TOKEN_EXPIRY_TIMEOUT = 7

# Role-based access control constants
ADMIN_PANEL_ROLES = {
    "super_admin": True,
    "admin": True,
    "business_owner": True,
}

APP_ROLES = {
    "super_admin": False,
    "admin": False,
    "staff": True,
    "business_owner": False,
}

# General constants
EMAIL = "email"
ACCOUNT_TYPE = "account_type"
USER = "user"
USER_ID = "user_id"
ACCESS_TOKEN = "access_token"
FULL_NAME = "full_name"
LOGIN_SUCCESS = "Login successfully"

ERROR = "error"
SUCCESS = "success"
MESSAGE = "message"
STATUS = "status"
PAYLOAD_FILE = "payload_file"
DATA = "data"


ERROR_GENERATING_PRE_SIGNED_URL = "Error generating presigned URLs"
EXPIRATION = 3600
PRE_SIGNED_URL = "Presigned URL"


SOURCE = "source"
AWS = "aws"
MOCK = "mock"


# Timezone and other constants
DEFAULT_TIMEZONE = "America/New_York"

# Email service constants
MAILGUN_API_URL = "https://api.mailgun.net/v3/{}/messages"

# Backward compatibility constants
SOMETHING_WENT_WRONG = "Something went wrong."

# Redis constants
CACHE_EXPIRY_TIMEOUT = 3600

# Regex for password validation: requires 8-16 chars with at least one digit, lowercase, uppercase, and special character
PASSWORD_REGEX = re.compile(r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$%&!]).+$")


FILTER = {
    "ALL": "all",
    "ACTIVE": "active",
    "INACTIVE": "inactive",
}
