# ✅ Correction Applied

## 🔧 Issue Found & Fixed

**Issue:** When status is **INACTIVE** and **EXPIRED**, the Status Change Modal should open (not Edit Modal).

**Root Cause:** The condition was checking only `promo.expired` without checking the status.

**Fix Applied:** Updated the condition to check `status === ACTIVE AND expired === true`

---

## 📝 Code Change

### File: `PromoCode.tsx`

**BEFORE:**
```typescript
if (promo && promo.expired && action === PROMO_CODE_ACTION.ACTIVATE) {
    // Open Edit Modal
    setEditPromoData(promo);
    setIsEditMode(true);
    setIsPromoModalOpen(true);
} else {
    // Open Status Change Modal
    setSelectedPromoCodeId(promoCodeId);
    setStatusChangeAction(action);
    setIsStatusChangeModalOpen(true);
}
```

**AFTER:**
```typescript
if (
    promo &&
    promo.status === PROMO_CODE_STATUS.ACTIVE &&
    promo.expired &&
    action === PROMO_CODE_ACTION.ACTIVATE
) {
    // Open Edit Modal
    setEditPromoData(promo);
    setIsEditMode(true);
    setIsPromoModalOpen(true);
} else {
    // Open Status Change Modal
    setSelectedPromoCodeId(promoCodeId);
    setStatusChangeAction(action);
    setIsStatusChangeModalOpen(true);
}
```

---

## 🎯 Updated Decision Logic

```
User clicks "Activate" button
    ↓
Check: status === ACTIVE AND expired === true AND action === ACTIVATE?
    ├─ YES → Open EDIT MODAL
    └─ NO  → Open STATUS CHANGE MODAL
```

---

## 📊 Scenario Matrix

| Status | Expired | Action | Result |
|--------|---------|--------|--------|
| ACTIVE | YES | ACTIVATE | **EDIT MODAL** ✅ |
| ACTIVE | YES | DEACTIVATE | Status Modal |
| ACTIVE | NO | ACTIVATE | Status Modal |
| ACTIVE | NO | DEACTIVATE | Status Modal |
| INACTIVE | YES | ACTIVATE | Status Modal ✅ |
| INACTIVE | YES | DEACTIVATE | Status Modal |
| INACTIVE | NO | ACTIVATE | Status Modal |
| INACTIVE | NO | DEACTIVATE | Status Modal |

---

## ✅ Test Cases

### Test Case 1: ACTIVE + EXPIRED + ACTIVATE
```
Status: ACTIVE
Expired: YES
Click: Activate
Expected: EDIT MODAL opens ✅
```

### Test Case 2: INACTIVE + EXPIRED + ACTIVATE
```
Status: INACTIVE
Expired: YES
Click: Activate
Expected: STATUS CHANGE MODAL opens ✅
```

### Test Case 3: INACTIVE + EXPIRED + DEACTIVATE
```
Status: INACTIVE
Expired: YES
Click: Deactivate
Expected: STATUS CHANGE MODAL opens ✅
```

### Test Case 4: ACTIVE + NOT EXPIRED + ACTIVATE
```
Status: ACTIVE
Expired: NO
Click: Activate
Expected: STATUS CHANGE MODAL opens ✅
```

---

## 🔐 Validation

The condition now properly checks:
1. ✅ Promo status is ACTIVE
2. ✅ Promo is EXPIRED
3. ✅ Action is ACTIVATE

All three conditions must be TRUE for Edit Modal to open.

---

## 📝 Example Data

Your example data:
```json
{
    "id": 5,
    "name": "Kessie",
    "status": "inactive",
    "expired": true,
    "inactive": true,
    "active": false
}
```

**Result:** Status Change Modal opens ✅ (because status is INACTIVE)

---

## ✅ Status

**Correction Applied:** ✅ COMPLETE

All code has been updated and verified. No syntax errors.

Ready for testing!

