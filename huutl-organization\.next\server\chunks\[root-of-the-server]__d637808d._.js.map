{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/constants/routes.ts"], "sourcesContent": ["const ROUTES = {\r\n\tHOME: \"/\",\r\n\t<PERSON><PERSON><PERSON><PERSON>: \"/login\",\r\n\tSIGNU<PERSON>: \"/signup\",\r\n\tFORGOT_PASSWORD: \"/forgot-password\",\r\n\tVERIFY: \"/verification\",\r\n\tRESET_PASSWORD: \"/reset-password\",\r\n\tNO_INTERNET: \"/no-internet\",\r\n\tDASHBOARD: \"/dashboard\",\r\n};\r\n\r\n// Routes that should only be accessible when user is NOT logged in\r\nexport const BEFORE_LOGIN_ROUTES = [\r\n\tROUTES.LOGIN,\r\n\tROUTES.SIGNUP,\r\n\tROUTES.FORGOT_PASSWORD,\r\n\tROUTES.VERIFY,\r\n\tROUTES.RESET_PASSWORD,\r\n];\r\n\r\n// Routes that are accessible to everyone (logged in or not)\r\nexport const COMMON_ROUTES = [ROUTES.NO_INTERNET];\r\n\r\nexport default ROUTES;\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,SAAS;IACd,MAAM;IACN,OAAO;IACP,QAAQ;IACR,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,aAAa;IACb,WAAW;AACZ;AAGO,MAAM,sBAAsB;IAClC,OAAO,KAAK;IACZ,OAAO,MAAM;IACb,OAAO,eAAe;IACtB,OAAO,MAAM;IACb,OAAO,cAAc;CACrB;AAGM,MAAM,gBAAgB;IAAC,OAAO,WAAW;CAAC;uCAElC", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/constants/endpoint.ts"], "sourcesContent": ["const URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst endpoint = {\r\n\tauth: {\r\n\t\tSIGNIN: `${URL}/auth/login`,\r\n\t\tSIGNUP: `${URL}/auth/signup`,\r\n\t\tVERIFY_OTP: `${URL}/auth/verify-otp`,\r\n\t\tRESEND_OTP: `${URL}/auth/resend-verification`,\r\n\t\tFORGOT_PASSWORD: `${URL}/auth/forgot-password`,\r\n\t\tRESET_PASSWORD: `${URL}/auth/reset-password`,\r\n\t\tDELETE_SESSION: `${URL}/auth/delete-session`,\r\n\t},\r\n};\r\n\r\nexport default endpoint;\r\n"], "names": [], "mappings": ";;;;AAAA,MAAM,MAAM,QAAQ,GAAG,CAAC,oBAAoB;AAE5C,MAAM,WAAW;IAChB,MAAM;QACL,QAAQ,GAAG,IAAI,WAAW,CAAC;QAC3B,QAAQ,GAAG,IAAI,YAAY,CAAC;QAC5B,YAAY,GAAG,IAAI,gBAAgB,CAAC;QACpC,YAAY,GAAG,IAAI,yBAAyB,CAAC;QAC7C,iBAAiB,GAAG,IAAI,qBAAqB,CAAC;QAC9C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;QAC5C,gBAAgB,GAAG,IAAI,oBAAoB,CAAC;IAC7C;AACD;uCAEe", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/constants/commonConstants.ts"], "sourcesContent": ["export const NETWORK_STATUS = {\r\n\tONLINE: \"online\",\r\n\tOFFLINE: \"offline\",\r\n};\r\n\r\nexport const PASSWORD_REGEX =\r\n\t/^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\\s).{8,16}$/;\r\nexport const EMAIL_REGEX =\r\n\t/^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]{0,63}[a-zA-Z0-9])?@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,30}[a-zA-Z0-9])?\\.){1,2}[a-zA-Z]{2,6}$/;\r\n\r\nexport const ACTION_TYPE = {\r\n\tSIGNUP: \"signup\",\r\n\tFORGOT_PASSWORD: \"forgot-password\",\r\n};\r\n\r\nexport const DEFAULT_LIMIT = 20;\r\nexport const SEARCH_CONSTANTS = /^[A-Za-z\\s]+$/;\r\nexport const RESEND_OTP_TIMER = 30;\r\n\r\nexport const StorageKeys = {\r\n\tREMEMBER_ME: \"rememberMe\",\r\n};\r\n\r\nexport const ACCOUNT_TYPE = {\r\n\tADMIN: \"org_admin\",\r\n\tSTAFF: \"employee\",\r\n};\r\n\r\nexport const ORG_NAME_ALLOWED_ONLY_CHARACTERS = /^[A-Za-z0-9\\s]+$/;\r\nexport const US_PHONE_NUMBER_VALIDATION = /^\\+1 \\(\\d{3}\\) \\d{3}-\\d{4}$/;\r\nexport const FORMAT_NUMBER_USA = \"+1 (###) ###-####\";\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,iBAAiB;IAC7B,QAAQ;IACR,SAAS;AACV;AAEO,MAAM,iBACZ;AACM,MAAM,cACZ;AAEM,MAAM,cAAc;IAC1B,QAAQ;IACR,iBAAiB;AAClB;AAEO,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AAEzB,MAAM,cAAc;IAC1B,aAAa;AACd;AAEO,MAAM,eAAe;IAC3B,OAAO;IACP,OAAO;AACR;AAEO,MAAM,mCAAmC;AACzC,MAAM,6BAA6B;AACnC,MAAM,oBAAoB", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/auth.ts"], "sourcesContent": ["import { AuthOptions } from \"next-auth\";\r\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\r\nimport ROUT<PERSON> from \"./constants/routes\";\r\nimport endpoint from \"./constants/endpoint\";\r\nimport { ACTION_TYPE } from \"./constants/commonConstants\";\r\n\r\nconst auth: AuthOptions = {\r\n\tproviders: [\r\n\t\tCredentialsProvider({\r\n\t\t\tname: \"Credentials\",\r\n\t\t\tcredentials: {\r\n\t\t\t\temail: { label: \"Email\", type: \"email\" },\r\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\r\n\t\t\t\ttype: { label: \"Type\", type: \"text\" },\r\n\t\t\t\totp: { label: \"OTP\", type: \"text\" },\r\n\t\t\t\tpanel: { label: \"Panel\", type: \"text\" },\r\n\t\t\t},\r\n\r\n\t\t\tasync authorize(credentials) {\r\n\t\t\t\tconsole.log(\"🔐 NextAuth: Starting authorization...\");\r\n\r\n\t\t\t\tif (!credentials?.email) {\r\n\t\t\t\t\tconsole.log(\"❌ NextAuth: Missing credentials\");\r\n\t\t\t\t\tthrow new Error(\"Missing credentials\");\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst { email, password } = credentials as {\r\n\t\t\t\t\temail: string;\r\n\t\t\t\t\tpassword: string;\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconsole.log(\"🔐 NextAuth: Attempting login for:\", email);\r\n\t\t\t\tconsole.log(\"🔐 NextAuth: Attempting login for:\", password);\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log(\r\n\t\t\t\t\t\t\"🔐 NextAuth: Attempting login for:\",\r\n\t\t\t\t\t\tendpoint.auth.SIGNIN\r\n\t\t\t\t\t);\r\n\t\t\t\t\tconsole.log(\"🔐 NextAuth: credentials?.typer:\", credentials?.type);\r\n\t\t\t\t\tconst parms =\r\n\t\t\t\t\t\tcredentials?.type === ACTION_TYPE.SIGNUP\r\n\t\t\t\t\t\t\t? endpoint.auth.VERIFY_OTP\r\n\t\t\t\t\t\t\t: endpoint.auth.SIGNIN;\r\n\t\t\t\t\tconst bodyData =\r\n\t\t\t\t\t\tcredentials?.type !== ACTION_TYPE.SIGNUP\r\n\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\temail,\r\n\t\t\t\t\t\t\t\t\tpassword,\r\n\t\t\t\t\t\t\t\t\tpanel: credentials?.panel,\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t: {\r\n\t\t\t\t\t\t\t\t\temail,\r\n\t\t\t\t\t\t\t\t\totp: credentials?.otp,\r\n\t\t\t\t\t\t\t\t\ttype: credentials?.type,\r\n\t\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconsole.log(\"🔐 NextAuth: Attempting login for parms:\", parms);\r\n\t\t\t\t\tconsole.log(\"🔐 NextAuth: Attempting login for bodyData:\", bodyData);\r\n\r\n\t\t\t\t\tconst res = await fetch(parms, {\r\n\t\t\t\t\t\tmethod: \"POST\",\r\n\t\t\t\t\t\theaders: {\r\n\t\t\t\t\t\t\t\"Content-Type\": \"application/json\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tbody: JSON.stringify(bodyData),\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log(\"🔐 NextAuth: API Response --->\", res);\r\n\r\n\t\t\t\t\tconst user = await res.json();\r\n\t\t\t\t\tconsole.log(\"🔐 NextAuth: API Response data:---->>>\", user);\r\n\r\n\t\t\t\t\tif (!res.ok) {\r\n\t\t\t\t\t\tconsole.log(\r\n\t\t\t\t\t\t\t\"❌ NextAuth: API request failed with status:\",\r\n\t\t\t\t\t\t\tres.status\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t\tthrow new Error(\"something_went_wrong\");\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconsole.log(\"🔐 NextAuth: API Response data:\", user);\r\n\r\n\t\t\t\t\t// Check if login was successful\r\n\t\t\t\t\tif (user && user.success) {\r\n\t\t\t\t\t\tconsole.log(\"✅ NextAuth: Login successful\");\r\n\t\t\t\t\t\treturn user;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Throw error with backend message for failed login\r\n\t\t\t\t\t\tconst errorMessage = user?.message || \"Login failed\";\r\n\t\t\t\t\t\tconsole.log(\"❌ NextAuth: Login failed -\", errorMessage);\r\n\t\t\t\t\t\tthrow new Error(errorMessage);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"❌ NextAuth: Error during authorization:\", error);\r\n\r\n\t\t\t\t\t// If it's an error we threw with a specific message, preserve it\r\n\t\t\t\t\tif (\r\n\t\t\t\t\t\terror instanceof Error &&\r\n\t\t\t\t\t\terror.message !== \"something_went_wrong\"\r\n\t\t\t\t\t) {\r\n\t\t\t\t\t\tthrow error;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Otherwise, throw generic error\r\n\t\t\t\t\tthrow new Error(\"something_went_wrong\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}),\r\n\t],\r\n\tsecret: process.env.NEXTAUTH_SECRET,\r\n\tpages: {\r\n\t\tsignIn: ROUTES.LOGIN,\r\n\t},\r\n\tcallbacks: {\r\n\t\tasync jwt({ token, user }) {\r\n\t\t\tconsole.log(\"🔐 NextAuth JWT Callback:\", {\r\n\t\t\t\thasUser: !!user,\r\n\t\t\t\ttokenSub: token.sub,\r\n\t\t\t\tuserData: (user as unknown as { data: unknown })?.data\r\n\t\t\t\t\t? \"present\"\r\n\t\t\t\t\t: \"missing\",\r\n\t\t\t});\r\n\r\n\t\t\t// If user is present (first time login), merge user data into token\r\n\t\t\tif (user) {\r\n\t\t\t\treturn { ...token, ...user };\r\n\t\t\t}\r\n\r\n\t\t\t// Return existing token for subsequent requests\r\n\t\t\treturn token;\r\n\t\t},\r\n\t\tasync session({ session, token }) {\r\n\t\t\tconsole.log(\"🔐 NextAuth Session Callback:\", {\r\n\t\t\t\ttokenSub: token.sub,\r\n\t\t\t\thasTokenData: !!token.data,\r\n\t\t\t});\r\n\r\n\t\t\t// Pass token data to session\r\n\t\t\tsession.user = token;\r\n\t\t\treturn session;\r\n\t\t},\r\n\t},\r\n};\r\n\r\nexport default auth;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;AAEA,MAAM,OAAoB;IACzB,WAAW;QACV,IAAA,qKAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;gBAChD,MAAM;oBAAE,OAAO;oBAAQ,MAAM;gBAAO;gBACpC,KAAK;oBAAE,OAAO;oBAAO,MAAM;gBAAO;gBAClC,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;YACvC;YAEA,MAAM,WAAU,WAAW;gBAC1B,QAAQ,GAAG,CAAC;gBAEZ,IAAI,CAAC,aAAa,OAAO;oBACxB,QAAQ,GAAG,CAAC;oBACZ,MAAM,IAAI,MAAM;gBACjB;gBAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAK5B,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,QAAQ,GAAG,CAAC,sCAAsC;gBAElD,IAAI;oBACH,QAAQ,GAAG,CACV,sCACA,yIAAQ,CAAC,IAAI,CAAC,MAAM;oBAErB,QAAQ,GAAG,CAAC,oCAAoC,aAAa;oBAC7D,MAAM,QACL,aAAa,SAAS,oJAAW,CAAC,MAAM,GACrC,yIAAQ,CAAC,IAAI,CAAC,UAAU,GACxB,yIAAQ,CAAC,IAAI,CAAC,MAAM;oBACxB,MAAM,WACL,aAAa,SAAS,oJAAW,CAAC,MAAM,GACrC;wBACA;wBACA;wBACA,OAAO,aAAa;oBACrB,IACC;wBACA;wBACA,KAAK,aAAa;wBAClB,MAAM,aAAa;oBACpB;oBAEH,QAAQ,GAAG,CAAC,4CAA4C;oBACxD,QAAQ,GAAG,CAAC,+CAA+C;oBAE3D,MAAM,MAAM,MAAM,MAAM,OAAO;wBAC9B,QAAQ;wBACR,SAAS;4BACR,gBAAgB;wBACjB;wBACA,MAAM,KAAK,SAAS,CAAC;oBACtB;oBAEA,QAAQ,GAAG,CAAC,kCAAkC;oBAE9C,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,QAAQ,GAAG,CAAC,0CAA0C;oBAEtD,IAAI,CAAC,IAAI,EAAE,EAAE;wBACZ,QAAQ,GAAG,CACV,+CACA,IAAI,MAAM;wBAEX,MAAM,IAAI,MAAM;oBACjB;oBAEA,QAAQ,GAAG,CAAC,mCAAmC;oBAE/C,gCAAgC;oBAChC,IAAI,QAAQ,KAAK,OAAO,EAAE;wBACzB,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACR,OAAO;wBACN,oDAAoD;wBACpD,MAAM,eAAe,MAAM,WAAW;wBACtC,QAAQ,GAAG,CAAC,8BAA8B;wBAC1C,MAAM,IAAI,MAAM;oBACjB;gBACD,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,2CAA2C;oBAEzD,iEAAiE;oBACjE,IACC,iBAAiB,SACjB,MAAM,OAAO,KAAK,wBACjB;wBACD,MAAM;oBACP;oBAEA,iCAAiC;oBACjC,MAAM,IAAI,MAAM;gBACjB;YACD;QACD;KACA;IACD,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACN,QAAQ,uIAAM,CAAC,KAAK;IACrB;IACA,WAAW;QACV,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACxB,QAAQ,GAAG,CAAC,6BAA6B;gBACxC,SAAS,CAAC,CAAC;gBACX,UAAU,MAAM,GAAG;gBACnB,UAAU,AAAC,MAAuC,OAC/C,YACA;YACJ;YAEA,oEAAoE;YACpE,IAAI,MAAM;gBACT,OAAO;oBAAE,GAAG,KAAK;oBAAE,GAAG,IAAI;gBAAC;YAC5B;YAEA,gDAAgD;YAChD,OAAO;QACR;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC/B,QAAQ,GAAG,CAAC,iCAAiC;gBAC5C,UAAU,MAAM,GAAG;gBACnB,cAAc,CAAC,CAAC,MAAM,IAAI;YAC3B;YAEA,6BAA6B;YAC7B,QAAQ,IAAI,GAAG;YACf,OAAO;QACR;IACD;AACD;uCAEe", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\r\nimport auth from \"@/auth\";\r\n\r\nconst handler = NextAuth(auth);\r\n\r\nexport { handler as GET, handler as POST };\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,UAAU,IAAA,kJAAQ,EAAC,wHAAI", "debugId": null}}]}