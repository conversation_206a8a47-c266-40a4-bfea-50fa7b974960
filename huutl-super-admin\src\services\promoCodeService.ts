/**
 * Promo Code Service
 *
 * Provides promo code-related API operations including creating, fetching,
 * updating status, and deleting promo codes.
 * Follows the same pattern as profileService.ts
 */

import endpoint from "../constants/endpoint";
import type {
	IPromoCodeCreateRequest,
	IPromoCodeQueryParams,
	IPromoCodeUpdateRequest,
} from "../interfaces/promoCodeInterface";

import { get, post, patch, remove } from "../utils/http";
import type Api<PERSON><PERSON>ponse from "../utils/IApiResponse";

/**
 * Get all promo codes with optional query parameters
 * @param params - Query parameters (search, skip, limit)
 * @returns Promise resolving to list of promo codes
 */
export const getAllPromoCodesService = async (
	params?: IPromoCodeQueryParams
): Promise<ApiResponse> => {
	return await get(endpoint.promoCodes.GET_ALL_PROMO_CODES, params);
};

/**
 * Create a new promo code
 * @param data - Promo code creation data
 * @returns Promise resolving to created promo code data
 */
export const createPromoCodeService = async (
	data: IPromoCodeCreateRequest
): Promise<ApiResponse> => {
	return await post(endpoint.promoCodes.CREATE_PROMO_CODE, data);
};

/**
 * Delete a promo code by ID
 * @param promoCodeId - ID of the promo code to delete
 * @returns Promise resolving to deletion response
 */
export const deletePromoCodeService = async (
	promoCodeId: number
): Promise<ApiResponse> => {
	return await remove(
		`${endpoint.promoCodes.DELETE_PROMO_CODE}/${promoCodeId}`
	);
};

/**
 * Update promo code status (activate/deactivate)
 * @param data - Promo code update data containing ID and new status
 * @returns Promise resolving to updated promo code data
 */
export const updatePromoCodeStatusService = async (
	data: IPromoCodeUpdateRequest
): Promise<ApiResponse> => {
	return await patch(endpoint.promoCodes.UPDATE_PROMO_CODE_STATUS, data);
};

/**
 * Update promo code dates (valid_from and valid_until)
 * @param promoCodeId - ID of the promo code to update
 * @param data - Promo code update data containing valid_from and valid_until
 * @returns Promise resolving to updated promo code data
 */
export const updatePromoCodeService = async (
	promoCodeId: number,
	data: { valid_from: string; valid_until: string }
): Promise<ApiResponse> => {
	return await patch(
		`${endpoint.promoCodes.UPDATE_PROMO_CODE_STATUS.replace("/status", "")}/${promoCodeId}`,
		data
	);
};
