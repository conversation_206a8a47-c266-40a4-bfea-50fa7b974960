{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from \"next-intl/routing\";\r\n\r\nexport const routing = defineRouting({\r\n\tlocales: [\"en\", \"de\"],\r\n\tdefaultLocale: \"en\",\r\n\tlocalePrefix: \"as-needed\",\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,UAAU,IAAA,mPAAa,EAAC;IACpC,SAAS;QAAC;QAAM;KAAK;IACrB,eAAe;IACf,cAAc;AACf", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/i18n/request.ts"], "sourcesContent": ["import { hasLocale } from \"next-intl\";\r\nimport { getRequestConfig } from \"next-intl/server\";\r\nimport { routing } from \"./routing\";\r\n\r\nexport default getRequestConfig(async ({ requestLocale }) => {\r\n\t// Typically corresponds to the `[locale]` segment\r\n\tconst requested = await requestLocale;\r\n\tconst locale = hasLocale(routing.locales, requested)\r\n\t\t? requested\r\n\t\t: routing.defaultLocale;\r\n\r\n\treturn {\r\n\t\tlocale,\r\n\t\tmessages: (await import(`../../messages/${locale}.json`)).default,\r\n\t};\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;uCAEe,IAAA,8QAAgB,EAAC,OAAO,EAAE,aAAa,EAAE;IACvD,kDAAkD;IAClD,MAAM,YAAY,MAAM;IACxB,MAAM,SAAS,IAAA,8LAAS,EAAC,iIAAO,CAAC,OAAO,EAAE,aACvC,YACA,iIAAO,CAAC,aAAa;IAExB,OAAO;QACN;QACA,UAAU,CAAC;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAClE;AACD", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/redux/ReduxProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/redux/ReduxProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/redux/ReduxProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/redux/ReduxProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/redux/ReduxProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/redux/ReduxProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/providers/InternetConnectionProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/providers/InternetConnectionProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/InternetConnectionProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2T,GACxV,yFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/providers/InternetConnectionProvider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/providers/InternetConnectionProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/InternetConnectionProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/providers/InternetWrapper.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/providers/InternetWrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/InternetWrapper.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/providers/InternetWrapper.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/providers/InternetWrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/InternetWrapper.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/layout/RouteAwareWrapper.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/RouteAwareWrapper.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/RouteAwareWrapper.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/components/layout/RouteAwareWrapper.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/RouteAwareWrapper.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/RouteAwareWrapper.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/huutl/huutl-organization/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\r\nimport \"@/styles/style.scss\";\r\nimport { ReactNode } from \"react\";\r\nimport { Locale, NextIntlClientProvider } from \"next-intl\";\r\nimport { getMessages } from \"next-intl/server\";\r\nimport ReduxProvider from \"@/redux/ReduxProvider\";\r\nimport InternetConnectionProvider from \"@/components/providers/InternetConnectionProvider\";\r\nimport InternetWrapper from \"@/components/providers/InternetWrapper\";\r\nimport RouteAwareWrapper from \"@/components/layout/RouteAwareWrapper\";\r\nimport { Toaster } from \"react-hot-toast\";\r\nimport \"../../../node_modules/bootstrap/dist/css/bootstrap.css\";\r\n\r\ntype Props = {\r\n\tchildren: ReactNode;\r\n\tparams: Promise<{ locale: Locale }>;\r\n};\r\n\r\nexport const metadata: Metadata = {\r\n\ttitle: \"Huutl\",\r\n\tdescription: \"Huutl Organization\",\r\n};\r\n\r\nexport default async function LocaleLayout({ children, params }: Props) {\r\n\tconst { locale } = await params;\r\n\tconst messages = await getMessages();\r\n\treturn (\r\n\t\t<html lang={locale}>\r\n\t\t\t<head>\r\n\t\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n\t\t\t\t<meta charSet=\"UTF-8\" />\r\n\t\t\t\t<meta name=\"description\" content=\"Huutl Organization\" />\r\n\t\t\t\t<meta name=\"keywords\" content=\"Huutl\" />\r\n\t\t\t\t<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\r\n\t\t\t\t<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" />\r\n\t\t\t\t<link\r\n\t\t\t\t\trel=\"icon\"\r\n\t\t\t\t\thref=\"/favicon.ico?v=1\"\r\n\t\t\t\t\ttype=\"image/x-icon\"\r\n\t\t\t\t\tsizes=\"any\"\r\n\t\t\t\t/>\r\n\t\t\t\t<link rel=\"shortcut icon\" href=\"/favicon.ico?v=1\" type=\"image/x-icon\" />\r\n\t\t\t\t<link rel=\"apple-touch-icon\" href=\"/favicon.ico?v=1\" />\r\n\t\t\t\t<link\r\n\t\t\t\t\thref=\"https://fonts.googleapis.com/css2?family=Aoboshi+One&family=Baskervville:ital,wght@0,400..700;1,400..700&family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap\"\r\n\t\t\t\t\trel=\"stylesheet\"\r\n\t\t\t\t/>\r\n\t\t\t</head>\r\n\t\t\t<body>\r\n\t\t\t\t<ReduxProvider>\r\n\t\t\t\t\t<NextIntlClientProvider locale={locale} messages={messages}>\r\n\t\t\t\t\t\t<InternetConnectionProvider>\r\n\t\t\t\t\t\t\t<InternetWrapper>\r\n\t\t\t\t\t\t\t\t<RouteAwareWrapper>{children}</RouteAwareWrapper>\r\n\t\t\t\t\t\t\t</InternetWrapper>\r\n\t\t\t\t\t\t\t<Toaster position=\"top-right\" />\r\n\t\t\t\t\t\t</InternetConnectionProvider>\r\n\t\t\t\t\t</NextIntlClientProvider>\r\n\t\t\t\t</ReduxProvider>\r\n\t\t\t</body>\r\n\t\t</html>\r\n\t);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAQO,MAAM,WAAqB;IACjC,OAAO;IACP,aAAa;AACd;AAEe,eAAe,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAS;IACrE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,WAAW,MAAM,IAAA,+PAAW;IAClC,qBACC,8OAAC;QAAK,MAAM;;0BACX,8OAAC;;kCACA,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,SAAQ;;;;;;kCACd,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBACA,KAAI;wBACJ,MAAK;wBACL,MAAK;wBACL,OAAM;;;;;;kCAEP,8OAAC;wBAAK,KAAI;wBAAgB,MAAK;wBAAmB,MAAK;;;;;;kCACvD,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBACA,MAAK;wBACL,KAAI;;;;;;;;;;;;0BAGN,8OAAC;0BACA,cAAA,8OAAC,yIAAa;8BACb,cAAA,8OAAC,4RAAsB;wBAAC,QAAQ;wBAAQ,UAAU;kCACjD,cAAA,8OAAC,wKAA0B;;8CAC1B,8OAAC,6JAAe;8CACf,cAAA,8OAAC,4JAAiB;kDAAE;;;;;;;;;;;8CAErB,8OAAC,kKAAO;oCAAC,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB", "debugId": null}}]}