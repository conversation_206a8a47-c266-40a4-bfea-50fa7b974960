# 📊 Visual Flow Guide - Edit Modal Implementation

## 🎯 Decision Tree

```
┌─────────────────────────────────────────────────────────────┐
│  User Clicks But<PERSON> on Promo Code                           │
└────────────────────┬────────────────────────────────────────┘
                     │
                     ▼
        ┌────────────────────────┐
        │ Check Promo State      │
        │ - Is Expired?          │
        │ - Action = Activate?   │
        └────────┬───────────────┘
                 │
        ┌────────┴────────┐
        │                 │
        ▼                 ▼
    YES & YES          NO or NO
        │                 │
        │                 │
        ▼                 ▼
   ┌─────────────┐   ┌──────────────────┐
   │ EDIT MODAL  │   │ STATUS CHANGE    │
   │ OPENS       │   │ MODAL OPENS      │
   └─────────────┘   └──────────────────┘
        │                 │
        ▼                 ▼
   ┌─────────────┐   ┌──────────────────┐
   │ Name: ❌    │   │ Confirmation     │
   │ Discount: ❌│   │ Message          │
   │ Dates: ✅   │   └──────────────────┘
   └─────────────┘        │
        │                 ▼
        │            ┌──────────────────┐
        │            │ User Confirms    │
        │            └──────────────────┘
        │                 │
        ▼                 ▼
   ┌─────────────┐   ┌──────────────────┐
   │ User Updates│   │ Status Updated   │
   │ Dates       │   │ (Active/Inactive)│
   └─────────────┘   └──────────────────┘
        │                 │
        ▼                 ▼
   ┌─────────────┐   ┌──────────────────┐
   │ Click       │   │ List Refreshes   │
   │ "Update"    │   └──────────────────┘
   └─────────────┘
        │
        ▼
   ┌─────────────┐
   │ Backend     │
   │ Validates & │
   │ Updates     │
   └─────────────┘
        │
        ▼
   ┌─────────────┐
   │ List        │
   │ Refreshes   │
   └─────────────┘
```

---

## 📋 Scenario Breakdown

### Scenario 1: Expired Promo Code + Activate Button
```
┌─────────────────────────────────────────────────────────┐
│ Promo Code: "SUMMER20"                                  │
│ Status: Active                                          │
│ Valid Until: 2024-10-15                                 │
│ Today: 2024-10-29                                       │
│ Expired: YES ✅                                         │
└─────────────────────────────────────────────────────────┘
                        │
                        ▼
            ┌───────────────────────┐
            │ User clicks "Activate"│
            └───────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Check: Expired + Activate?    │
        │ YES ✅                        │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ EDIT MODAL OPENS              │
        │ ✅ Form populated with data   │
        │ ❌ Name disabled              │
        │ ❌ Discount disabled          │
        │ ✅ Dates editable             │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ User updates dates:           │
        │ Valid From: 2024-11-01        │
        │ Valid Until: 2024-12-31       │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Click "Update Promo Code"     │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Backend validates:            │
        │ ✅ Promo exists               │
        │ ✅ Not expired                │
        │ ✅ Is active                  │
        │ ✅ Dates valid                │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Dates updated successfully    │
        │ ✅ Toast: "Updated"           │
        │ ✅ Modal closes               │
        │ ✅ List refreshes             │
        └───────────────────────────────┘
```

### Scenario 2: Inactive Promo Code + Activate Button
```
┌─────────────────────────────────────────────────────────┐
│ Promo Code: "WINTER20"                                  │
│ Status: Inactive                                        │
│ Valid Until: 2024-12-31                                 │
│ Today: 2024-10-29                                       │
│ Expired: NO ❌                                          │
└─────────────────────────────────────────────────────────┘
                        │
                        ▼
            ┌───────────────────────┐
            │ User clicks "Activate"│
            └───────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Check: Expired + Activate?    │
        │ NO ❌ (Not expired)           │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ STATUS CHANGE MODAL OPENS     │
        │ (Existing behavior)           │
        │ "Are you sure?"               │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ User confirms                 │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Status updated to Active      │
        │ ✅ Toast: "Status updated"    │
        │ ✅ Modal closes               │
        │ ✅ List refreshes             │
        └───────────────────────────────┘
```

### Scenario 3: Active Promo Code + Deactivate Button
```
┌─────────────────────────────────────────────────────────┐
│ Promo Code: "SPRING20"                                  │
│ Status: Active                                          │
│ Valid Until: 2024-12-31                                 │
│ Today: 2024-10-29                                       │
│ Expired: NO ❌                                          │
└─────────────────────────────────────────────────────────┘
                        │
                        ▼
            ┌───────────────────────┐
            │ User clicks           │
            │ "Deactivate"          │
            └───────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Check: Expired + Activate?    │
        │ NO ❌ (Action is Deactivate)  │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ STATUS CHANGE MODAL OPENS     │
        │ (Existing behavior)           │
        │ "Are you sure?"               │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ User confirms                 │
        └───────────────────────────────┘
                        │
                        ▼
        ┌───────────────────────────────┐
        │ Status updated to Inactive    │
        │ ✅ Toast: "Status updated"    │
        │ ✅ Modal closes               │
        │ ✅ List refreshes             │
        └───────────────────────────────┘
```

---

## 🔧 Code Logic

### Smart Routing Function
```typescript
const handleStatusChangeClick = (promoCodeId, action) => {
    const promo = promoCodesData.find((p) => p.id === promoCodeId);
    
    // CONDITION: Expired AND Activate
    if (promo && promo.expired && action === PROMO_CODE_ACTION.ACTIVATE) {
        // ✅ OPEN EDIT MODAL
        setEditPromoData(promo);
        setIsEditMode(true);
        setIsPromoModalOpen(true);
    } else {
        // ✅ OPEN STATUS CHANGE MODAL
        setSelectedPromoCodeId(promoCodeId);
        setStatusChangeAction(action);
        setIsStatusChangeModalOpen(true);
    }
};
```

---

## 📊 State Management

### When Edit Modal Opens
```javascript
{
    isEditMode: true,
    editPromoData: {
        id: 1,
        name: "SUMMER20",
        discount_value: 20,
        valid_from: "2024-10-01T00:00:00Z",
        valid_until: "2024-10-15T23:59:59Z",
        status: "active",
        expired: true
    },
    isPromoModalOpen: true
}
```

### When Status Change Modal Opens
```javascript
{
    selectedPromoCodeId: 1,
    statusChangeAction: "activate",
    isStatusChangeModalOpen: true
}
```

---

## ✅ Validation Checks

### Frontend Checks
- ✅ Promo exists in list
- ✅ Promo is expired
- ✅ Action is activate

### Backend Checks
- ✅ Promo code exists (404)
- ✅ Promo code not expired (400)
- ✅ Promo code is active (400)
- ✅ valid_until >= valid_from (400)

---

## 🎨 UI States

### Edit Modal - Disabled Fields
```
┌─────────────────────────────────────┐
│ Edit Promo Code                     │
├─────────────────────────────────────┤
│ Name: [SUMMER20] ❌ DISABLED        │
│ Discount: [20%] ❌ DISABLED         │
│ Valid From: [2024-11-01] ✅ ENABLED │
│ Valid Until: [2024-12-31] ✅ ENABLED│
├─────────────────────────────────────┤
│ [Cancel] [Update Promo Code]        │
└─────────────────────────────────────┘
```

### Status Change Modal
```
┌─────────────────────────────────────┐
│ Activate Promo Code                 │
├─────────────────────────────────────┤
│ Are you sure you want to change     │
│ the status?                         │
├─────────────────────────────────────┤
│ [Cancel] [Confirm]                  │
└─────────────────────────────────────┘
```

---

## 🚀 Complete Flow Summary

1. **User Action**: Click button on promo code
2. **Check Condition**: Is expired AND action is activate?
3. **Route Decision**:
   - YES → Open Edit Modal
   - NO → Open Status Change Modal
4. **Edit Modal Path**:
   - User updates dates
   - Backend validates
   - Dates updated
   - List refreshes
5. **Status Change Modal Path**:
   - User confirms
   - Status updated
   - List refreshes

---

## 📝 Testing Checklist

- [ ] Expired + Activate → Edit Modal opens
- [ ] Inactive + Activate → Status Change Modal opens
- [ ] Active + Deactivate → Status Change Modal opens
- [ ] Expired + Deactivate → Status Change Modal opens
- [ ] Edit Modal: Name disabled
- [ ] Edit Modal: Discount disabled
- [ ] Edit Modal: Dates editable
- [ ] Edit Modal: Update works
- [ ] List refreshes after edit
- [ ] List refreshes after status change

