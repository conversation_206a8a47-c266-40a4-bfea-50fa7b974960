/**
 * Promo code related interfaces for promo code management
 */

/**
 * Base promo code data structure
 */
export interface IPromoCode {
	id: number;
	name: string;
	discount_value: number;
	valid_from: string;
	valid_until: string;
	status: string;
	expired: boolean;
	inactive: boolean;
	active: boolean;
	created_at: string;
	updated_at?: string;
}

/**
 * Promo code form data for creation
 */
export interface IPromoCodeFormData {
	name: string;
	discountValue: number;
	validFrom: string;
	validUntil: string;
}

/**
 * Promo code create request payload
 */
export interface IPromoCodeCreateRequest {
	name: string;
	discount_value: number;
	valid_from: string;
	valid_until: string;
}

/**
 * Promo code update status request payload
 */
export interface IPromoCodeUpdateRequest {
	id: number;
	status: string;
}

/**
 * Promo code update dates request payload (for edit mode)
 */
export interface IPromoCodeUpdateDatesRequest {
	id: number;
	valid_from: string;
	valid_until: string;
}

/**
 * Query parameters for fetching promo codes
 */
export interface IPromoCodeQueryParams {
	skip?: number;
	limit?: number;
	search?: string;
}

/**
 * Promo code API response structure
 */
export interface IPromoCodeResponse {
	message: string;
	success: boolean;
	data: IPromoCode | IPromoCode[] | null;
	status_code: number;
	error?: Record<string, unknown>;
}

/**
 * Create/Edit promo code modal props
 */
export interface ICreateEditPromoCodeModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (data: IPromoCodeFormData) => void;
	isSubmitting?: boolean;
	isEditMode?: boolean;
	editData?: IPromoCode | null;
}

/**
 * Create promo code modal props (deprecated - use ICreateEditPromoCodeModalProps)
 */
export interface ICreatePromoCodeModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSubmit: (data: IPromoCodeFormData) => void;
	isSubmitting?: boolean;
}

/**
 * Form data structure for profile updates
 */
export interface IProfileFormData {
	name: string;
}

/**
 * Form data structure for password changes
 */
export interface IChangePasswordFormData {
	old_password: string;
	new_password: string;
	confirm_password: string;
}
