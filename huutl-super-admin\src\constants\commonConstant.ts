export const NETWORK_EVENTS = {
	ONLINE: "online",
	OFFLINE: "offline",
};

export const VALIDATION_MSG_CONST = {
	email_req: "Email is required",
	email_val_msg: "Please enter a valid email",
	pass_req: "Password is required",
	pass_val: "Please enter a valid password",
	phone_req: "Phone is Required!",
	valid_phone: "Please enter valid Phone!",
	otp_req: "Please enter 4-digit OTP sent to your mobile!",
	valid_otp: "Please enter valid OTP!",
	pass_not_match: "Password does not match!",
	// Organization validation messages
	org_name_req: "Organization name is required",
	org_name_alpha: "Organization name must contain only alphabets",
	org_name_min: "Organization name must be at least 3 characters",
	org_name_max: "Organization name must not exceed 50 characters",
	website_invalid:
		"Website must start with www, http or https and end with .com or .in",
	owner_name_req: "Owner name is required",
	owner_name_alpha: "Owner name must contain only alphabets",
	owner_name_min: "Owner name must be at least 3 characters",
	owner_name_max: "Owner name must not exceed 50 characters",
	employees_positive: "Number of employees must be a positive number",
	subscription_req: "Subscription plan is required",
	phone_format: "Phone number must be in format: +1 (XXX) XXX-XXXX",
	plan_expiry_req: "Plan expiry date is required",
	status_req: "Initial status is required",
};

export const REMEMBER_ME_KEY = "rememberme";

export const STATUS_OPTIONS = [
	{ value: "Active", label: "Active" },
	{ value: "Inactive", label: "Inactive" },
];

export const SUBSCRIPTION_OPTIONS = [
	{ value: "Basic", label: "Basic" },
	{ value: "Pro", label: "Pro" },
	{ value: "Premium", label: "Premium" },
];

export const TYPING_PREVENTION_CHARACTERS = [
	"%",
	"_",
	"\\",
	"@",
	"#",
	"$",
	"%",
	"^",
	"&",
	"*",
	"!",
];

export const SUBSCRIPTION_CLASS_OPTIONS = {
	BASIC: "basic",
	PRO: "pro",
	PREMIUM: "premium",
};

export const STATUS_CLASS_OPTIONS = {
	ACTIVE: "active",
	INACTIVE: "inactive",
	ARCHIVE: "archive",
	PENDING: "pending",
};

export const ADD = "add";

export const LIMIT = 10;

export const FORMAT_NUMBER_USA = "+1 (###) ###-####";

export const DEFAULT_DATE_FORMAT = "YYYY-MM-DD";

export const TAB_OPTIONS = {
	ALL: "all",
	ACTIVE: "active",
	INACTIVE: "inactive",
	PENDING: "pending",
};

export const DOLLAR_SIGN = "$";

export const ACCEPT_IMAGE = ["image/png", "image/jpeg", "image/jpg"];

export const PROMO_CODE_STATUS = {
	ACTIVE: "active",
	INACTIVE: "inactive",
	EXPIRED: "expired",
};

export const TIMEZONE_OFFSET = "T00:00:00Z";
export const TIMEZONE_OFFSET_END = "T23:59:59Z";

export const PROMO_CODE_ACTION = {
	ACTIVATE: "activate",
	DEACTIVATE: "deactivate",
} as const;

export const SETTINGS_TABS = {
	PROFILE: "profile",
	PROMO: "promo",
	SUBSCRIPTION: "subscription",
} as const;
