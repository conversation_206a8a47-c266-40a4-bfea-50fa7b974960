# Edit Modal Flow - Quick Reference Guide

## 📋 Requirement Summary

**When user clicks Activate button on an EXPIRED promo code:**
- ✅ Edit Modal opens (NOT status change modal)
- ✅ User can update dates only
- ✅ Name and discount are disabled
- ✅ After update, dates are refreshed

**For all other cases:**
- ✅ Status Change Modal opens (existing behavior)

---

## 🔄 Decision Logic

```
User clicks Activate button on a promo code
    ↓
Check: Is promo code EXPIRED?
    ├─ YES + Action is ACTIVATE
    │   └─→ Open EDIT MODAL (to update dates)
    │
    └─ NO or Action is DEACTIVATE
        └─→ Open STATUS CHANGE MODAL (existing behavior)
```

---

## 💻 Code Implementation

### Location: `PromoCode.tsx` - `handleStatusChangeClick()` function

```typescript
const handleStatusChangeClick = (
    promoCodeId: number,
    action: PromoCodeActionType
) => {
    const promo = promoCodesData.find((p) => p.id === promoCodeId);

    // If expired and trying to activate, open edit modal
    if (promo && promo.expired && action === PROMO_CODE_ACTION.ACTIVATE) {
        setEditPromoData(promo);
        setIsEditMode(true);
        setIsPromoModalOpen(true);
    } else {
        // Otherwise, open status change confirmation modal
        setSelectedPromoCodeId(promoCodeId);
        setStatusChangeAction(action);
        setIsStatusChangeModalOpen(true);
    }
};
```

---

## 🎯 Scenario Examples

### Scenario 1: Expired Promo Code - Click Activate
```
Promo Code: "SUMMER20"
Status: Active
Expired: YES (valid_until = 2024-10-15, today = 2024-10-29)

User Action: Click "Activate" button
Result: EDIT MODAL OPENS
- Form shows current dates
- User can change valid_from and valid_until
- Name and discount are DISABLED
- User clicks "Update Promo Code"
- Dates are updated
- List refreshes
```

### Scenario 2: Inactive Promo Code - Click Activate
```
Promo Code: "WINTER20"
Status: Inactive
Expired: NO

User Action: Click "Activate" button
Result: STATUS CHANGE MODAL OPENS
- Shows confirmation message
- User confirms
- Status changes to Active
- List refreshes
```

### Scenario 3: Active Promo Code - Click Deactivate
```
Promo Code: "SPRING20"
Status: Active
Expired: NO

User Action: Click "Deactivate" button
Result: STATUS CHANGE MODAL OPENS
- Shows confirmation message
- User confirms
- Status changes to Inactive
- List refreshes
```

### Scenario 4: Expired Promo Code - Click Deactivate
```
Promo Code: "FALL20"
Status: Active
Expired: YES

User Action: Click "Deactivate" button
Result: STATUS CHANGE MODAL OPENS
- Shows confirmation message
- User confirms
- Status changes to Inactive
- List refreshes
```

---

## 🧪 Testing Steps

### Test 1: Edit Expired Promo Code
1. Create promo code with valid_until = today
2. Wait for next day (or manually set date to past)
3. Promo code should show as "Expired"
4. Click "Activate" button
5. ✅ Edit Modal should open (NOT status change modal)
6. ✅ Name and discount should be disabled
7. ✅ Dates should be editable
8. Update dates to future dates
9. Click "Update Promo Code"
10. ✅ List should refresh with new dates

### Test 2: Activate Inactive Promo Code
1. Create promo code and deactivate it
2. Status should show "Inactive"
3. Click "Activate" button
4. ✅ Status Change Modal should open
5. Confirm activation
6. ✅ Status should change to "Active"

### Test 3: Deactivate Active Promo Code
1. Create promo code (status = Active)
2. Click "Deactivate" button
3. ✅ Status Change Modal should open
4. Confirm deactivation
5. ✅ Status should change to "Inactive"

### Test 4: Deactivate Expired Promo Code
1. Create promo code with past valid_until date
2. Status should show "Expired"
3. Click "Deactivate" button
4. ✅ Status Change Modal should open (NOT edit modal)
5. Confirm deactivation
6. ✅ Status should change to "Inactive"

---

## 📊 State Management

### Edit Mode State
```typescript
const [isEditMode, setIsEditMode] = useState(false);
const [editPromoData, setEditPromoData] = useState<IPromoCode | null>(null);
```

### When Edit Modal Opens
```typescript
setEditPromoData(promo);      // Store promo code data
setIsEditMode(true);           // Set edit mode flag
setIsPromoModalOpen(true);     // Open modal
```

### When Edit Modal Closes
```typescript
setIsPromoModalOpen(false);
setIsEditMode(false);
setEditPromoData(null);
```

---

## 🔐 Backend Validation

The backend `update_promo_code()` function validates:
1. ✅ Promo code exists
2. ✅ Promo code is not expired
3. ✅ Promo code is active
4. ✅ valid_until >= valid_from

If any validation fails, error message is returned to frontend.

---

## ✨ Key Points

- ✅ No separate Edit button in UI
- ✅ Edit modal opens conditionally based on promo state
- ✅ Smart routing: Expired + Activate = Edit, Otherwise = Status Change
- ✅ User-friendly: Clear indication of what can be edited
- ✅ Backend protected: Multiple validation checks
- ✅ Seamless UX: Same modal for both create and edit

---

## 📁 Files Modified

1. `huutl-super-admin/src/views/settings/PromoCode.tsx` - Smart routing logic
2. `huutl-super-admin/src/views/settings/CreatePromoCodeModal.tsx` - Edit mode support
3. `huutl-super-admin/src/services/promoCodeService.ts` - Update service
4. `huutl-backend/app/features/admin/promo_codes/repository.py` - Update function
5. `huutl-backend/app/features/admin/promo_codes/routes.py` - Update endpoint
6. `huutl-super-admin/src/i18n/locales/en/translation.json` - Translation keys

---

## 🚀 Ready for Testing

All implementation is complete. Follow the testing steps above to verify the functionality works as expected.

