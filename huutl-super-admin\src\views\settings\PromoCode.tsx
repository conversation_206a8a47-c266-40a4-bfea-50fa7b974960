import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";

import Button from "../../components/formElements/Button";
import { CommonTable } from "../../components/commonTable/CommonTable";
import CopyIcon from "../../components/svgElements/CopyIcon";
import DeleteIcon from "../../components/svgElements/DeleteIcon";
import PlusIcon from "../../components/svgElements/PlusIcon";

import CreateEditPromoCodeModal from "./CreatePromoCodeModal";
import DeletePromoCodeModal from "./DeletePromoCodeModal";
import StatusChangePromoCodeModal from "./StatusChangePromoCodeModal";

import type {
	IPromoCode,
	IPromoCodeFormData,
	IPromoCodeCreateRequest,
} from "../../interfaces/promoCodeInterface";

import {
	getAllPromoCodesService,
	createPromoCodeService,
	deletePromoCodeService,
	updatePromoCodeStatusService,
	updatePromoCodeService,
} from "../../services/promoCodeService";

import {
	toastMessageSuccess,
	toastMessageError,
	toTitleCase,
} from "../../utils/helper";
import {
	LIMIT,
	PROMO_CODE_ACTION,
	PROMO_CODE_STATUS,
	TIMEZONE_OFFSET,
	TIMEZONE_OFFSET_END,
} from "../../constants/commonConstant";
import type { PromoCodeActionType } from "../../types/commonTypes";

const PromoCode = () => {
	const { t } = useTranslation();
	// State management
	const [promoCodesData, setPromoCodesData] = useState<IPromoCode[]>([]);
	const [isPromoModalOpen, setIsPromoModalOpen] = useState(false);
	const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
	const [isStatusChangeModalOpen, setIsStatusChangeModalOpen] = useState(false);
	const [selectedPromoCodeId, setSelectedPromoCodeId] = useState<number | null>(
		null
	);
	const [statusChangeAction, setStatusChangeAction] =
		useState<PromoCodeActionType>(PROMO_CODE_ACTION.ACTIVATE);
	const [isEditMode, setIsEditMode] = useState(false);
	const [editPromoData, setEditPromoData] = useState<IPromoCode | null>(null);
	const [loading, setLoading] = useState({
		isFetching: false,
		isSubmitting: false,
		isDeleting: false,
		isUpdating: false,
	});
	const [hasMore, setHasMore] = useState(true);
	const [currentPage, setCurrentPage] = useState(1);

	// Handle copy promo code
	const handleCopyCode = (code: string) => {
		navigator.clipboard.writeText(code);
		toastMessageSuccess(
			`${t("promo_code")} "${code?.toUpperCase()}" ${t("copied_to_clipboard")}`
		);
	};

	// Get status class for badges
	const getStatusClass = (promo: IPromoCode) => {
		// If inactive and expired, show grey badge
		if (promo.status === PROMO_CODE_STATUS.INACTIVE && promo.expired)
			return "badge badge-grey";
		// If active and expired, show red badge
		if (promo.status === PROMO_CODE_STATUS.ACTIVE && promo.expired)
			return "badge badge-danger";
		if (promo.inactive) return "badge badge-grey";
		if (promo.active) return "badge badge-green";
		return "badge";
	};

	// Get status text
	const getStatusText = (promo: IPromoCode) => {
		if (promo.expired && promo.status === PROMO_CODE_STATUS.ACTIVE)
			return PROMO_CODE_STATUS.EXPIRED;
		if (promo.status === PROMO_CODE_STATUS.INACTIVE)
			return PROMO_CODE_STATUS.INACTIVE;
		if (promo.status === PROMO_CODE_STATUS.ACTIVE)
			return PROMO_CODE_STATUS.ACTIVE;
		return "N/A";
	};

	// Fetch promo codes from API
	const fetchPromoCodes = useCallback(
		async (reset = false) => {
			try {
				setLoading((prev) => ({ ...prev, isFetching: true }));

				if (reset) {
					setPromoCodesData([]);
					setCurrentPage(1);
				}

				const pageToFetch = reset ? 1 : currentPage;
				const skip = (pageToFetch - 1) * LIMIT;

				const response = await getAllPromoCodesService({
					skip,
					limit: LIMIT,
				});

				if (response?.success && response?.data) {
					const newData = Array.isArray(response.data)
						? (response.data as IPromoCode[])
						: [];
					if (reset) {
						setPromoCodesData(newData);
					} else {
						setPromoCodesData((prev) => [...prev, ...newData]);
					}

					setHasMore(newData.length === LIMIT);
				} else {
					setHasMore(false);
				}
			} catch (error) {
				console.error("Error fetching promo codes:", error);
				toastMessageError(t("something_went_wrong"));
				setHasMore(false);
			} finally {
				setLoading((prev) => ({ ...prev, isFetching: false }));
			}
		},
		[currentPage]
	);

	// Load more promo codes for infinite scroll
	const loadMore = () => {
		if (!loading.isFetching && hasMore) {
			setCurrentPage((prev) => prev + 1);
		}
	};

	// Fetch promo codes on mount
	useEffect(() => {
		fetchPromoCodes(true);
	}, []);

	// Fetch more data when currentPage changes
	useEffect(() => {
		if (currentPage > 1) {
			fetchPromoCodes(false);
		}
	}, [currentPage, fetchPromoCodes]);

	// Handle promo code creation
	const handlePromoCodeSubmit = async (data: IPromoCodeFormData) => {
		try {
			setLoading((prev) => ({ ...prev, isSubmitting: true }));

			// Convert date strings to ISO datetime format for backend
			// Parse dates properly to avoid timezone issues
			const validFromDate = new Date(data.validFrom + TIMEZONE_OFFSET);
			const validUntilDate = new Date(data.validUntil + TIMEZONE_OFFSET_END);

			const createData: IPromoCodeCreateRequest = {
				name: data.name,
				discount_value: data.discountValue,
				valid_from: validFromDate.toISOString(),
				valid_until: validUntilDate.toISOString(),
			};

			const response = await createPromoCodeService(createData);

			if (response?.success) {
				toastMessageSuccess(response?.message);
				setIsPromoModalOpen(false);
				fetchPromoCodes(true); // Refresh the list
			} else {
				toastMessageError(response.message);
			}
		} catch (error) {
			console.error("Error creating promo code:", error);
			toastMessageError(t("something_went_wrong"));
		} finally {
			setLoading((prev) => ({ ...prev, isSubmitting: false }));
		}
	};

	// Handle promo code delete button click - open confirmation modal
	const handlePromoCodeDeleteClick = (promoCodeId: number) => {
		setSelectedPromoCodeId(promoCodeId);
		setIsDeleteModalOpen(true);
	};

	// Handle confirmed deletion
	const handleConfirmDelete = async () => {
		if (selectedPromoCodeId === null) return;

		try {
			setLoading((prev) => ({ ...prev, isDeleting: true }));

			const response = await deletePromoCodeService(selectedPromoCodeId);

			if (response.success) {
				toastMessageSuccess(response.message);
				// Remove from local state
				setPromoCodesData((prev) =>
					prev.filter((promo) => promo.id !== selectedPromoCodeId)
				);
				setIsDeleteModalOpen(false);
				setSelectedPromoCodeId(null);
			} else {
				toastMessageError(response.message);
			}
		} catch (error) {
			console.error("Error deleting promo code:", error);
			toastMessageError(t("something_went_wrong"));
		} finally {
			setLoading((prev) => ({ ...prev, isDeleting: false }));
		}
	};

	// Handle promo code edit submission
	const handlePromoCodeEditSubmit = async (data: IPromoCodeFormData) => {
		if (!editPromoData) return;

		try {
			setLoading((prev) => ({ ...prev, isSubmitting: true }));

			// Convert date strings to ISO datetime format for backend
			const validFromDate = new Date(data.validFrom + TIMEZONE_OFFSET);
			const validUntilDate = new Date(data.validUntil + TIMEZONE_OFFSET_END);

			const updateData = {
				valid_from: validFromDate.toISOString(),
				valid_until: validUntilDate.toISOString(),
			};

			const response = await updatePromoCodeService(editPromoData.id, updateData);

			if (response?.success) {
				toastMessageSuccess(response?.message);
				setIsPromoModalOpen(false);
				setIsEditMode(false);
				setEditPromoData(null);
				fetchPromoCodes(true); // Refresh the list
			} else {
				toastMessageError(response.message);
			}
		} catch (error) {
			console.error("Error updating promo code:", error);
			toastMessageError(t("something_went_wrong"));
		} finally {
			setLoading((prev) => ({ ...prev, isSubmitting: false }));
		}
	};

	// Handle status change button click
	// If status is ACTIVE and expired and action is ACTIVATE, open edit modal to update dates
	// Otherwise, open status change confirmation modal
	const handleStatusChangeClick = (
		promoCodeId: number,
		action: PromoCodeActionType
	) => {
		const promo = promoCodesData.find((p) => p.id === promoCodeId);

		// If status is ACTIVE and expired and trying to activate, open edit modal
		if (
			promo &&
			promo.status === PROMO_CODE_STATUS.ACTIVE &&
			promo.expired &&
			action === PROMO_CODE_ACTION.ACTIVATE
		) {
			setEditPromoData(promo);
			setIsEditMode(true);
			setIsPromoModalOpen(true);
		} else {
			// Otherwise, open status change confirmation modal
			setSelectedPromoCodeId(promoCodeId);
			setStatusChangeAction(action);
			setIsStatusChangeModalOpen(true);
		}
	};

	// Handle confirmed status change
	const handleConfirmStatusChange = async () => {
		if (selectedPromoCodeId === null) return;

		const newStatus =
			statusChangeAction === PROMO_CODE_ACTION.ACTIVATE
				? PROMO_CODE_STATUS.ACTIVE
				: PROMO_CODE_STATUS.INACTIVE;

		try {
			setLoading((prev) => ({ ...prev, isUpdating: true }));

			const response = await updatePromoCodeStatusService({
				id: selectedPromoCodeId,
				status: newStatus,
			});

			if (response.success) {
				toastMessageSuccess(response.message);
				// Update local state with both status and boolean flags
				setPromoCodesData((prev) =>
					prev.map((promo) =>
						promo.id === selectedPromoCodeId
							? {
									...promo,
									status: newStatus,
									active: newStatus === PROMO_CODE_STATUS.ACTIVE,
									inactive: newStatus === PROMO_CODE_STATUS.INACTIVE,
									expired: false,
								}
							: promo
					)
				);
				setIsStatusChangeModalOpen(false);
				setSelectedPromoCodeId(null);
				setStatusChangeAction(PROMO_CODE_ACTION.ACTIVATE);
			} else {
				toastMessageError(response.message);
			}
		} catch (error) {
			console.error("Error updating promo code status:", error);
			toastMessageError(t("something_went_wrong"));
		} finally {
			setLoading((prev) => ({ ...prev, isUpdating: false }));
		}
	};

	// Define table columns
	const columns = [
		{
			header: t("code"),
			render: (item: IPromoCode) => (
				<div className="code-cell">
					<span className="code-text">{item?.name?.toUpperCase()}</span>
					<Button
						className="copy-btn"
						onClick={() => handleCopyCode(item?.name)}
						title={t("copy_code")}
					>
						<CopyIcon />
					</Button>
				</div>
			),
		},
		{
			header: t("discount"),
			render: (item: IPromoCode) => (
				<span className="strong-text">{item?.discount_value}%</span>
			),
		},
		{
			header: t("valid_period"),
			render: (item: IPromoCode) => (
				<div>
					<div className="strong-text">
						{new Date(item?.valid_from).toISOString().slice(0, 10)}
					</div>
					<div className="small-text">
						to {new Date(item?.valid_until).toISOString().slice(0, 10)}
					</div>
				</div>
			),
		},
		{
			header: t("status"),
			render: (item: IPromoCode) => (
				<div>
					<span className={getStatusClass(item)}>
						{toTitleCase(getStatusText(item))}
					</span>
				</div>
			),
		},
		{
			header: t("actions"),
			render: (item: IPromoCode) => (
				<div className="d-inline-flex gap-3">
					{/* Activate/Deactivate button */}
					{item.status === PROMO_CODE_STATUS.INACTIVE ||
					(item.status === PROMO_CODE_STATUS.ACTIVE && item.expired) ? (
						<Button
							className="clear-btn p-0 color-green"
							onClick={() =>
								handleStatusChangeClick(item.id, PROMO_CODE_ACTION.ACTIVATE)
							}
							disabled={loading.isUpdating}
						>
							{t("activate")}
						</Button>
					) : (
						<Button
							className="clear-btn p-0 color-warning"
							onClick={() =>
								handleStatusChangeClick(item.id, PROMO_CODE_ACTION.DEACTIVATE)
							}
							disabled={loading.isUpdating}
						>
							{t("deactivate")}
						</Button>
					)}

					{/* Delete button */}
					<Button
						className="icon-outline-btn p-0"
						onClick={() => handlePromoCodeDeleteClick(item.id)}
						disabled={loading.isDeleting}
					>
						<DeleteIcon />
					</Button>
				</div>
			),
		},
	];

	return (
		<>
			<div className="settings-content">
				<div className="promo-header">
					<div className="promo-header-content">
						<h2 className="promo-title">{t("promo_code_management")}</h2>
						<p className="promo-subtitle">
							{t("create_and_manage_discount_codes_for_subscription_plans")}
						</p>
					</div>
					<Button
						className="primary-btn radius-md"
						onClick={() => setIsPromoModalOpen(true)}
						disabled={loading.isSubmitting}
					>
						<PlusIcon className="me-2" />
						{t("create_promo_code")}
					</Button>
				</div>
			</div>
			<div className="mt-4">
				<CommonTable
					columns={columns}
					data={promoCodesData}
					isLoading={loading.isFetching}
					loadMore={loadMore}
					hasMore={hasMore}
					height={280}
				/>
			</div>

			{/* Create/Edit Promo Code Modal */}
			<CreateEditPromoCodeModal
				isOpen={isPromoModalOpen}
				onClose={() => {
					setIsPromoModalOpen(false);
					setIsEditMode(false);
					setEditPromoData(null);
				}}
				onSubmit={isEditMode ? handlePromoCodeEditSubmit : handlePromoCodeSubmit}
				isSubmitting={loading.isSubmitting}
				isEditMode={isEditMode}
				editData={editPromoData}
			/>

			{/* Delete Promo Code Modal */}
			<DeletePromoCodeModal
				isOpen={isDeleteModalOpen}
				onClose={() => {
					setIsDeleteModalOpen(false);
					setSelectedPromoCodeId(null);
				}}
				onConfirm={handleConfirmDelete}
				isDeleting={loading.isDeleting}
			/>

			{/* Status Change Promo Code Modal */}
			<StatusChangePromoCodeModal
				isOpen={isStatusChangeModalOpen}
				onClose={() => {
					setIsStatusChangeModalOpen(false);
					setSelectedPromoCodeId(null);
					setStatusChangeAction(PROMO_CODE_ACTION.ACTIVATE);
				}}
				onConfirm={handleConfirmStatusChange}
				isUpdating={loading.isUpdating}
				action={statusChangeAction}
			/>
		</>
	);
};

export default PromoCode;
