module.exports = [
"[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/querystring [external] (querystring, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}),
"[externals]/buffer [external] (buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/constants/routes.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "BEFORE_LOGIN_ROUTES",
    ()=>BEFORE_LOGIN_ROUTES,
    "COMMON_ROUTES",
    ()=>COMMON_ROUTES,
    "default",
    ()=>__TURBOPACK__default__export__
]);
const ROUTES = {
    HOME: "/",
    LOGIN: "/login",
    SIGNUP: "/signup",
    FORGOT_PASSWORD: "/forgot-password",
    VERIFY: "/verification",
    RESET_PASSWORD: "/reset-password",
    NO_INTERNET: "/no-internet",
    DASHBOARD: "/dashboard"
};
const BEFORE_LOGIN_ROUTES = [
    ROUTES.LOGIN,
    ROUTES.SIGNUP,
    ROUTES.FORGOT_PASSWORD,
    ROUTES.VERIFY,
    ROUTES.RESET_PASSWORD
];
const COMMON_ROUTES = [
    ROUTES.NO_INTERNET
];
const __TURBOPACK__default__export__ = ROUTES;
}),
"[project]/src/constants/endpoint.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
const URL = process.env.NEXT_PUBLIC_BASE_URL;
const endpoint = {
    auth: {
        SIGNIN: `${URL}/auth/login`,
        SIGNUP: `${URL}/auth/signup`,
        VERIFY_OTP: `${URL}/auth/verify-otp`,
        RESEND_OTP: `${URL}/auth/resend-verification`,
        FORGOT_PASSWORD: `${URL}/auth/forgot-password`,
        RESET_PASSWORD: `${URL}/auth/reset-password`,
        DELETE_SESSION: `${URL}/auth/delete-session`
    }
};
const __TURBOPACK__default__export__ = endpoint;
}),
"[project]/src/constants/commonConstants.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ACCOUNT_TYPE",
    ()=>ACCOUNT_TYPE,
    "ACTION_TYPE",
    ()=>ACTION_TYPE,
    "DEFAULT_LIMIT",
    ()=>DEFAULT_LIMIT,
    "EMAIL_REGEX",
    ()=>EMAIL_REGEX,
    "FORMAT_NUMBER_USA",
    ()=>FORMAT_NUMBER_USA,
    "NETWORK_STATUS",
    ()=>NETWORK_STATUS,
    "ORG_NAME_ALLOWED_ONLY_CHARACTERS",
    ()=>ORG_NAME_ALLOWED_ONLY_CHARACTERS,
    "PASSWORD_REGEX",
    ()=>PASSWORD_REGEX,
    "RESEND_OTP_TIMER",
    ()=>RESEND_OTP_TIMER,
    "SEARCH_CONSTANTS",
    ()=>SEARCH_CONSTANTS,
    "StorageKeys",
    ()=>StorageKeys,
    "US_PHONE_NUMBER_VALIDATION",
    ()=>US_PHONE_NUMBER_VALIDATION
]);
const NETWORK_STATUS = {
    ONLINE: "online",
    OFFLINE: "offline"
};
const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;
const EMAIL_REGEX = /^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]{0,63}[a-zA-Z0-9])?@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,30}[a-zA-Z0-9])?\.){1,2}[a-zA-Z]{2,6}$/;
const ACTION_TYPE = {
    SIGNUP: "signup",
    FORGOT_PASSWORD: "forgot-password"
};
const DEFAULT_LIMIT = 20;
const SEARCH_CONSTANTS = /^[A-Za-z\s]+$/;
const RESEND_OTP_TIMER = 30;
const StorageKeys = {
    REMEMBER_ME: "rememberMe"
};
const ACCOUNT_TYPE = {
    ADMIN: "org_admin",
    STAFF: "employee"
};
const ORG_NAME_ALLOWED_ONLY_CHARACTERS = /^[A-Za-z0-9\s]+$/;
const US_PHONE_NUMBER_VALIDATION = /^\+1 \(\d{3}\) \d{3}-\d{4}$/;
const FORMAT_NUMBER_USA = "+1 (###) ###-####";
}),
"[project]/src/auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/endpoint.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-route] (ecmascript)");
;
;
;
;
const auth = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: "Credentials",
            credentials: {
                email: {
                    label: "Email",
                    type: "email"
                },
                password: {
                    label: "Password",
                    type: "password"
                },
                type: {
                    label: "Type",
                    type: "text"
                },
                otp: {
                    label: "OTP",
                    type: "text"
                },
                panel: {
                    label: "Panel",
                    type: "text"
                }
            },
            async authorize (credentials) {
                console.log("🔐 NextAuth: Starting authorization...");
                if (!credentials?.email) {
                    console.log("❌ NextAuth: Missing credentials");
                    throw new Error("Missing credentials");
                }
                const { email, password } = credentials;
                console.log("🔐 NextAuth: Attempting login for:", email);
                console.log("🔐 NextAuth: Attempting login for:", password);
                try {
                    console.log("🔐 NextAuth: Attempting login for:", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].auth.SIGNIN);
                    console.log("🔐 NextAuth: credentials?.typer:", credentials?.type);
                    const parms = credentials?.type === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ACTION_TYPE"].SIGNUP ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].auth.VERIFY_OTP : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$endpoint$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].auth.SIGNIN;
                    const bodyData = credentials?.type !== __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ACTION_TYPE"].SIGNUP ? {
                        email,
                        password,
                        panel: credentials?.panel
                    } : {
                        email,
                        otp: credentials?.otp,
                        type: credentials?.type
                    };
                    console.log("🔐 NextAuth: Attempting login for parms:", parms);
                    console.log("🔐 NextAuth: Attempting login for bodyData:", bodyData);
                    const res = await fetch(parms, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: JSON.stringify(bodyData)
                    });
                    console.log("🔐 NextAuth: API Response --->", res);
                    const user = await res.json();
                    console.log("🔐 NextAuth: API Response data:---->>>", user);
                    if (!res.ok) {
                        console.log("❌ NextAuth: API request failed with status:", res.status);
                        throw new Error("something_went_wrong");
                    }
                    console.log("🔐 NextAuth: API Response data:", user);
                    // Check if login was successful
                    if (user && user.success) {
                        console.log("✅ NextAuth: Login successful");
                        return user;
                    } else {
                        // Throw error with backend message for failed login
                        const errorMessage = user?.message || "Login failed";
                        console.log("❌ NextAuth: Login failed -", errorMessage);
                        throw new Error(errorMessage);
                    }
                } catch (error) {
                    console.error("❌ NextAuth: Error during authorization:", error);
                    // If it's an error we threw with a specific message, preserve it
                    if (error instanceof Error && error.message !== "something_went_wrong") {
                        throw error;
                    }
                    // Otherwise, throw generic error
                    throw new Error("something_went_wrong");
                }
            }
        })
    ],
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].LOGIN
    },
    callbacks: {
        async jwt ({ token, user }) {
            console.log("🔐 NextAuth JWT Callback:", {
                hasUser: !!user,
                tokenSub: token.sub,
                userData: user?.data ? "present" : "missing"
            });
            // If user is present (first time login), merge user data into token
            if (user) {
                return {
                    ...token,
                    ...user
                };
            }
            // Return existing token for subsequent requests
            return token;
        },
        async session ({ session, token }) {
            console.log("🔐 NextAuth Session Callback:", {
                tokenSub: token.sub,
                hasTokenData: !!token.data
            });
            // Pass token data to session
            session.user = token;
            return session;
        }
    }
};
const __TURBOPACK__default__export__ = auth;
}),
"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>handler,
    "POST",
    ()=>handler
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/auth.ts [app-route] (ecmascript)");
;
;
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]);
;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__d637808d._.js.map