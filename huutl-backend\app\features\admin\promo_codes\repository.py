"""Repository for promo code operations."""

from datetime import datetime, timezone
from typing import Optional

from fastapi import status
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.schema import ResponseModal
from app.core.utils.constants import RESPONSE_MESSAGES
from app.models.promo_code import PromoCode, PromoCodeStatus

from .schema import PromoCodeCreate, PromoCodeUpdate


async def create_promo_code(
    db: AsyncSession, promo_code_data: PromoCodeCreate
) -> ResponseModal:
    """
    Create a new promo code.

    Args:
        db: Database session
        promo_code_data: Promo code data for creation

    Returns:
        Response with created promo code data or error message
    """
    try:
        # Check if promo code name already exists
        existing_promo = db.execute(
            select(PromoCode).filter(PromoCode.name == promo_code_data.name)
        )
        if existing_promo.scalar_one_or_none():
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NAME_ALREADY_EXISTS"],
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Create new promo code
        new_promo_code = PromoCode(
            name=promo_code_data.name,
            discount=promo_code_data.discount_value,
            valid_from=promo_code_data.valid_from,
            valid_to=promo_code_data.valid_until,
            status=PromoCodeStatus.active,
        )

        db.add(new_promo_code)
        db.commit()
        db.refresh(new_promo_code)

        # Prepare response data with status flags
        response_data = {
            "id": new_promo_code.id,
            "name": new_promo_code.name,
            "discount_value": new_promo_code.discount,
            "valid_from": new_promo_code.valid_from,
            "valid_until": new_promo_code.valid_to,
            "status": new_promo_code.status.value,
            "expired": _is_expired(new_promo_code),
            "inactive": new_promo_code.status == PromoCodeStatus.inactive,
            "active": new_promo_code.status == PromoCodeStatus.active and not _is_expired(new_promo_code),
            "created_at": new_promo_code.created_at,
            "updated_at": new_promo_code.updated_at,
        }

        return ResponseModal(
            success=True,
            data=response_data,
            message=RESPONSE_MESSAGES["PROMO_CODE_CREATED_SUCCESSFULLY"],
            error=None,
            status_code=status.HTTP_201_CREATED,
        )

    except Exception as e:
        db.rollback()
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


async def get_promo_codes(
    db: AsyncSession,
    skip: int = 0,
    limit: int = 10,
    search: Optional[str] = None,
) -> ResponseModal:
    """
    Get all promo codes with optional search and pagination.

    Args:
        db: Database session
        skip: Number of promo codes to skip
        limit: Number of promo codes to return
        search: Search query for promo code names

    Returns:
        Response with list of promo codes or error message
    """
    try:
        query = select(PromoCode).order_by(PromoCode.id.desc()).offset(skip).limit(limit)

        # Apply search filter if provided
        if search:
            search_term = f"%{search.strip()}%"
            query = query.filter(PromoCode.name.ilike(search_term))

        result = db.execute(query)
        promo_codes = result.scalars().all()

        if not promo_codes:
            return ResponseModal(
                success=True,
                data=[],
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_200_OK,
            )

        # Prepare response data with status flags
        promo_codes_data = []
        for promo_code in promo_codes:
            is_expired = _is_expired(promo_code)
            promo_codes_data.append({
                "id": promo_code.id,
                "name": promo_code.name,
                "discount_value": promo_code.discount,
                "valid_from": promo_code.valid_from,
                "valid_until": promo_code.valid_to,
                "status": promo_code.status.value,
                "expired": is_expired,
                "inactive": promo_code.status == PromoCodeStatus.inactive,
                "active": promo_code.status == PromoCodeStatus.active and not is_expired,
                "created_at": promo_code.created_at,
                "updated_at": promo_code.updated_at,
            })

        return ResponseModal(
            success=True,
            data=promo_codes_data,
            message=RESPONSE_MESSAGES["PROMO_CODE_FETCHED_SUCCESSFULLY"],
            error=None,
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


async def delete_promo_code(db: AsyncSession, promo_code_id: int) -> ResponseModal:
    """
    Delete a promo code by ID.

    Args:
        db: Database session
        promo_code_id: ID of the promo code to delete

    Returns:
        Response with success message or error message
    """
    try:
        # Check if promo code exists
        existing_promo = db.execute(
            select(PromoCode).filter(PromoCode.id == promo_code_id)
        )
        promo_code = existing_promo.scalar_one_or_none()

        if not promo_code:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_404_NOT_FOUND,
            )

        # Delete the promo code (hard delete)
        db.execute(delete(PromoCode).filter(PromoCode.id == promo_code_id))
        db.commit()

        return ResponseModal(
            success=True,
            data=None,
            message=RESPONSE_MESSAGES["PROMO_CODE_DELETED_SUCCESSFULLY"],
            error=None,
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        db.rollback()
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


async def update_promo_code_status(
    db: AsyncSession, promo_code_data: PromoCodeUpdate
) -> ResponseModal:
    """
    Update promo code status (activate/deactivate).

    Args:
        db: Database session
        promo_code_data: Promo code update data

    Returns:
        Response with updated promo code data or error message
    """
    try:
        # Check if promo code exists
        existing_promo = db.execute(
            select(PromoCode).filter(PromoCode.id == promo_code_data.id)
        )
        promo_code = existing_promo.scalar_one_or_none()
        print("promo_code===================>", promo_code.status.value)
        print("promo_code_data===================>", PromoCodeStatus.active.value)
        if not promo_code:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_404_NOT_FOUND,
            )
        # check valid until date is expired . if expired, return coupon is expired
        if(_is_expired(promo_code) and (promo_code.status.value == PromoCodeStatus.inactive.value)):
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_EXPIRED"],
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Update the status
        promo_code.status = PromoCodeStatus(promo_code_data.status)
        db.commit()
        db.refresh(promo_code)

        # Prepare response data with status flags
        is_expired = _is_expired(promo_code)
        response_data = {
            "id": promo_code.id,
            "name": promo_code.name,
            "discount_value": promo_code.discount,
            "valid_from": promo_code.valid_from,
            "valid_until": promo_code.valid_to,
            "status": promo_code.status.value,
            "expired": is_expired,
            "inactive": promo_code.status == PromoCodeStatus.inactive,
            "active": promo_code.status == PromoCodeStatus.active and not is_expired,
            "created_at": promo_code.created_at,
            "updated_at": promo_code.updated_at,
        }

        return ResponseModal(
            success=True,
            data=response_data,
            message=RESPONSE_MESSAGES["PROMO_CODE_STATUS_UPDATED_SUCCESSFULLY"],
            error=None,
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        db.rollback()
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


async def update_promo_code(
    db: AsyncSession, promo_code_id: int, promo_code_data: PromoCodeCreate
) -> ResponseModal:
    """
    Update promo code dates (valid_from and valid_until).
    Only allows updating dates when promo code is active and not expired.

    Args:
        db: Database session
        promo_code_id: ID of the promo code to update
        promo_code_data: Promo code data with new dates

    Returns:
        Response with updated promo code data or error message
    """
    try:
        # Check if promo code exists
        result = db.execute(
            select(PromoCode).filter(PromoCode.id == promo_code_id)
        )
        promo_code = result.scalar_one_or_none()

        if not promo_code:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES["PROMO_CODE_NOT_FOUND"],
                error=None,
                status_code=status.HTTP_404_NOT_FOUND,
            )
        # Check if promo code is active
        if promo_code.status != PromoCodeStatus.active:
            return ResponseModal(
                success=False,
                data=None,
                message=RESPONSE_MESSAGES.get(
                    "PROMO_CODE_NOT_ACTIVE",
                    "Promo code must be active to edit dates"
                ),
                error=None,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Update the dates
        promo_code.valid_from = promo_code_data.valid_from
        promo_code.valid_to = promo_code_data.valid_until
        db.commit()
        db.refresh(promo_code)

        # Prepare response data with status flags
        is_expired = _is_expired(promo_code)
        response_data = {
            "id": promo_code.id,
            "name": promo_code.name,
            "discount_value": promo_code.discount,
            "valid_from": promo_code.valid_from,
            "valid_until": promo_code.valid_to,
            "status": promo_code.status.value,
            "expired": is_expired,
            "inactive": promo_code.status == PromoCodeStatus.inactive,
            "active": promo_code.status == PromoCodeStatus.active and not is_expired,
            "created_at": promo_code.created_at,
            "updated_at": promo_code.updated_at,
        }

        return ResponseModal(
            success=True,
            data=response_data,
            message=RESPONSE_MESSAGES.get(
                "PROMO_CODE_UPDATED_SUCCESSFULLY",
                "Promo code updated successfully"
            ),
            error=None,
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        db.rollback()
        return ResponseModal(
            success=False,
            data=None,
            message=RESPONSE_MESSAGES["INTERNAL_SERVER_ERROR"],
            error={"detail": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


def _is_expired(promo_code: PromoCode) -> bool:
    """
    Check if a promo code is expired based on its valid_to date.

    Args:
        promo_code: PromoCode instance

    Returns:
        True if expired, False otherwise
    """
    # Use timezone-aware datetime to compare with database datetime
    return datetime.now(timezone.utc) > promo_code.valid_to
