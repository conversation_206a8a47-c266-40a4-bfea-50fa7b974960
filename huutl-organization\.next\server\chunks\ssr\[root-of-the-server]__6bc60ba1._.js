module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "authSlice",
    ()=>authSlice,
    "default",
    ()=>__TURBOPACK__default__export__,
    "selectProfileData",
    ()=>selectProfileData,
    "setAuthData",
    ()=>setAuthData,
    "updateUserPreferences",
    ()=>updateUserPreferences,
    "updateUserProfileData",
    ()=>updateUserProfileData
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
;
const initialState = {
    authData: {
        email: "",
        id: -1,
        name: "",
        userPreferences: {
            preferences: []
        },
        imageUrl: "",
        isActive: false,
        socialSignPlatform: ""
    }
};
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "auth",
    initialState,
    reducers: {
        setAuthData: (state, action)=>{
            state.authData = action.payload;
        },
        updateUserProfileData: (state, action)=>{
            if (state.authData) {
                const { name, image, userPreferences } = action.payload;
                // Update firstName and lastName separately
                if (name !== undefined) {
                    state.authData.name = name;
                }
                // Update image if provided
                if (image !== undefined) {
                    state.authData.imageUrl = image;
                }
                if (userPreferences && userPreferences.preferences && userPreferences.preferences.length > 0) {
                    state.authData.userPreferences.preferences = userPreferences.preferences;
                }
            }
        },
        updateUserPreferences: (state, action)=>{
            console.log(">>>>>>>>>>>>action", action);
            if (state.authData) {
                // Convert array to comma-separated string before storing
                state.authData.userPreferences.preferences = action.payload;
            }
        }
    }
});
const selectProfileData = (state)=>state.auth.authData;
const { setAuthData, updateUserProfileData, updateUserPreferences } = authSlice.actions;
const __TURBOPACK__default__export__ = authSlice.reducer;
}),
"[project]/src/redux/store.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "persistor",
    ()=>persistor,
    "store",
    ()=>store
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistStore.js [app-ssr] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/persistReducer.js [app-ssr] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/constants.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/lib/storage/index.js [app-ssr] (ecmascript)"); // defaults to localStorage for web
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/slices/authSlice.ts [app-ssr] (ecmascript)");
;
;
;
;
// Configure persist options for auth slice
const authPersistConfig = {
    key: "auth",
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]
};
// Create persisted reducers
const persistedAuthReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(authPersistConfig, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        auth: persistedAuthReducer
    },
    middleware: (getDefaultMiddleware)=>getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: [
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FLUSH"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REHYDRATE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PAUSE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERSIST"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PURGE"],
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$constants$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGISTER"]
                ]
            }
        })
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
 // Configure persist options for walkthrough slice
}),
"[project]/src/redux/ReduxProvider.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux-persist/es/integration/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const ReduxProvider = ({ children })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["store"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PersistGate"], {
            persistor: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persistor"],
            children: children
        }, void 0, false, {
            fileName: "[project]/src/redux/ReduxProvider.tsx",
            lineNumber: 14,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/redux/ReduxProvider.tsx",
        lineNumber: 13,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = ReduxProvider;
}),
"[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ACCOUNT_TYPE",
    ()=>ACCOUNT_TYPE,
    "ACTION_TYPE",
    ()=>ACTION_TYPE,
    "DEFAULT_LIMIT",
    ()=>DEFAULT_LIMIT,
    "EMAIL_REGEX",
    ()=>EMAIL_REGEX,
    "FORMAT_NUMBER_USA",
    ()=>FORMAT_NUMBER_USA,
    "NETWORK_STATUS",
    ()=>NETWORK_STATUS,
    "ORG_NAME_ALLOWED_ONLY_CHARACTERS",
    ()=>ORG_NAME_ALLOWED_ONLY_CHARACTERS,
    "PASSWORD_REGEX",
    ()=>PASSWORD_REGEX,
    "RESEND_OTP_TIMER",
    ()=>RESEND_OTP_TIMER,
    "SEARCH_CONSTANTS",
    ()=>SEARCH_CONSTANTS,
    "StorageKeys",
    ()=>StorageKeys,
    "US_PHONE_NUMBER_VALIDATION",
    ()=>US_PHONE_NUMBER_VALIDATION
]);
const NETWORK_STATUS = {
    ONLINE: "online",
    OFFLINE: "offline"
};
const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;
const EMAIL_REGEX = /^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]{0,63}[a-zA-Z0-9])?@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,30}[a-zA-Z0-9])?\.){1,2}[a-zA-Z]{2,6}$/;
const ACTION_TYPE = {
    SIGNUP: "signup",
    FORGOT_PASSWORD: "forgot-password"
};
const DEFAULT_LIMIT = 20;
const SEARCH_CONSTANTS = /^[A-Za-z\s]+$/;
const RESEND_OTP_TIMER = 30;
const StorageKeys = {
    REMEMBER_ME: "rememberMe"
};
const ACCOUNT_TYPE = {
    ADMIN: "org_admin",
    STAFF: "employee"
};
const ORG_NAME_ALLOWED_ONLY_CHARACTERS = /^[A-Za-z0-9\s]+$/;
const US_PHONE_NUMBER_VALIDATION = /^\+1 \(\d{3}\) \d{3}-\d{4}$/;
const FORMAT_NUMBER_USA = "+1 (###) ###-####";
}),
"[project]/src/hooks/useInternetConnection.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "useInternetConnection",
    ()=>useInternetConnection
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
"use client";
;
;
const useInternetConnection = ()=>{
    const [isOnline, setIsOnline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Just track the online/offline state
        // Initial check
        setIsOnline(navigator.onLine);
        const handleOnline = ()=>{
            console.log("Internet restored");
            setIsOnline(true);
        };
        const handleOffline = ()=>{
            console.log("Internet lost===================>");
            setIsOnline(false);
        // No redirect needed - InternetWrapper will show no-internet page
        };
        window.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].ONLINE, handleOnline);
        window.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].OFFLINE, handleOffline);
        return ()=>{
            window.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].ONLINE, handleOnline);
            window.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].OFFLINE, handleOffline);
        };
    }, []); // Remove router and pathname dependencies
    return {
        isOnline
    };
};
}),
"[project]/src/components/providers/InternetConnectionProvider.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>InternetConnectionProvider
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useInternetConnection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useInternetConnection.ts [app-ssr] (ecmascript)");
"use client";
;
;
function InternetConnectionProvider({ children }) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useInternetConnection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInternetConnection"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/utils/translationUtils.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Translation utility functions for handling dynamic backend keys
 */ __turbopack_context__.s([
    "useTranslate",
    ()=>useTranslate,
    "useTranslateBackendKey",
    ()=>useTranslateBackendKey
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
;
const useTranslate = ()=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])();
    /**
	 * Safely translates dynamic keys from backend responses
	 * @param key The dynamic key to translate
	 * @returns Translated string
	 */ const translate = (key)=>{
        if (!key) return "";
        try {
            // Cast only at this point, keeping type checking everywhere else
            return t(key);
        } catch (error) {
            // Fallback to the key itself if translation fails
            console.warn(`Translation failed for key: ${key}`, error);
            return key;
        }
    };
    return translate;
};
const useTranslateBackendKey = useTranslate;
}),
"[project]/src/components/svgElements/NoInternetIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "NoInternetIcon",
    ()=>NoInternetIcon
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function NoInternetIcon() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "350",
        height: "350",
        viewBox: "0 0 440 444",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                style: {
                    mixBlendMode: "luminosity"
                },
                clipPath: "url(#clip0_17873_14827)",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M165.1 16.0918C165.61 12.3518 160.34 9.28179 156.38 8.96179C154.49 8.81179 152.53 9.10179 150.74 8.47179C146.86 7.12179 145.31 2.11179 141.53 0.511788C138.7 -0.678212 135.45 0.381788 132.7 1.76179C129.96 3.14179 127.25 4.88179 124.18 5.07179C121.52 5.24179 118.62 4.25179 116.29 5.55179C114.55 6.52179 113.64 8.52179 112.04 9.70179C110.47 10.8618 108.43 11.1018 106.49 11.2118C104.55 11.3218 102.53 11.3418 100.76 12.1518C98.9899 12.9618 97.5299 14.8318 97.8899 16.7418L165.09 16.0818L165.1 16.0918Z",
                        fill: "#F5F5F5"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 15,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M430.61 236.792C431.47 230.582 422.7 225.472 416.11 224.942C412.97 224.692 409.71 225.172 406.73 224.132C400.28 221.882 397.71 213.552 391.42 210.902C386.72 208.922 381.3 210.682 376.74 212.982C372.18 215.282 367.67 218.162 362.58 218.492C358.15 218.772 353.34 217.132 349.46 219.292C346.57 220.902 345.06 224.232 342.39 226.202C339.78 228.122 336.39 228.532 333.15 228.702C329.92 228.882 326.57 228.912 323.63 230.262C320.69 231.612 318.25 234.722 318.86 237.902L430.6 236.802L430.61 236.792Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 19,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M97.2001 85.2314C97.8501 80.4814 91.1501 76.5714 86.1101 76.1614C83.7101 75.9714 81.2101 76.3314 78.9301 75.5414C74.0001 73.8214 72.0301 67.4514 67.2101 65.4214C63.6101 63.9014 59.4701 65.2514 55.9801 67.0114C52.4901 68.7714 49.0401 70.9814 45.1501 71.2214C41.7601 71.4414 38.0801 70.1814 35.1101 71.8414C32.9001 73.0714 31.7401 75.6214 29.7101 77.1214C27.7201 78.5914 25.1201 78.9014 22.6401 79.0414C20.1701 79.1714 17.6101 79.2014 15.3601 80.2314C13.1101 81.2614 11.2501 83.6414 11.7101 86.0714L97.1901 85.2314H97.2001Z",
                        fill: "#F5F5F5"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 23,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M362.67 344.094L340.43 440.684L349.83 440.664L372.13 345.004L362.67 344.094Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 27,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M403.26 344.094L425.59 440.684H434.52L412.92 344.094H403.26Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 31,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M347.66 271.543L360.33 338.043H374.72L361.19 271.543H347.66Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 35,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M127.03 344.094L108.88 440.684H118.12L137.07 344.094H127.03Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 39,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M165.18 344.094L185.93 440.154H195.06L175.02 344.094H165.18Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 43,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M112.01 271.543L124.68 338.043H139.07L125.54 271.543H112.01Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 47,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M83.5103 265.492L86.1003 277.582H375.15L371.77 265.492H83.5103Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 51,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M89.0303 288.973L92.0803 301.063H379.18L376.57 288.973H89.0303Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 55,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M94.73 313.281L97.58 325.371H383.78L381.08 313.281H94.73Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 59,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M425.25 338.043H116.62V350.133H425.25V338.043Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 63,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M300.19 188.302C295.29 188.302 290.9 185.772 288.43 181.532C285.96 177.282 285.94 172.192 288.39 167.932L327.38 99.922C329.83 95.652 334.22 93.102 339.14 93.082H339.18C344.08 93.082 348.47 95.612 350.94 99.852L390.34 167.622C392.81 171.872 392.83 176.962 390.38 181.222C387.93 185.492 383.54 188.042 378.62 188.062L300.23 188.302H300.19ZM339.18 97.152H339.15C335.7 97.162 332.62 98.952 330.9 101.952L291.91 169.962C290.2 172.952 290.21 176.522 291.94 179.502C293.67 182.472 296.75 184.242 300.19 184.242H300.22L378.61 184.002C382.06 183.992 385.14 182.202 386.86 179.202C388.57 176.212 388.56 172.642 386.83 169.662L347.43 101.892C345.7 98.922 342.62 97.152 339.18 97.152Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 67,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M331.53 126.583L333.35 151.943C333.5 154.013 335.17 155.653 337.25 155.763C339.44 155.873 341.33 154.253 341.56 152.063L344.16 126.533C344.36 124.603 342.82 122.933 340.87 122.973L334.68 123.113C332.83 123.153 331.4 124.733 331.53 126.573V126.583Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 71,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M344.1 169.973C343.05 173.233 339.56 175.013 336.3 173.963C333.04 172.913 331.26 169.423 332.31 166.163C333.36 162.903 336.85 161.123 340.11 162.173C343.37 163.223 345.15 166.713 344.1 169.973Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 75,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M439.61 440.561C439.61 440.701 341.19 440.821 219.81 440.821C98.4302 440.821 -0.00976562 440.701 -0.00976562 440.561C-0.00976562 440.421 98.3902 440.301 219.81 440.301C341.23 440.301 439.61 440.421 439.61 440.561Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 79,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M287.32 333.28L186.36 333.22C174.22 333.22 164.38 322.87 164.38 310.11L164.43 44.1295C164.43 31.3595 174.29 21.0195 186.43 21.0195L287.39 21.0795C299.53 21.0795 309.37 31.4395 309.37 44.1995L309.32 310.19C309.32 322.96 299.46 333.3 287.32 333.3V333.28Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 83,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M287.29 30.4814H267.72C265.1 30.4714 262.97 32.8014 262.97 35.6914V39.4214C262.97 42.3014 260.84 44.6414 258.22 44.6414L220.07 44.6214C217.45 44.6214 215.32 42.2814 215.32 39.4014V35.6714C215.32 32.7914 213.19 30.4514 210.57 30.4514H203.69L186.56 30.4414C178.05 30.4414 171.15 37.6814 171.15 46.6214L171.1 306.241C171.1 315.181 177.99 322.421 186.49 322.431L287.25 322.491C295.76 322.491 302.66 315.251 302.66 306.311L302.71 46.6914C302.71 37.7514 295.82 30.5114 287.32 30.5014L287.29 30.4814Z",
                        fill: "#828282"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 87,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M198.61 297.971V304.331C198.61 304.881 198 305.221 197.54 304.921L192.54 301.741C192.11 301.471 192.11 300.831 192.54 300.561L197.54 297.381C198 297.081 198.61 297.421 198.61 297.971Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 91,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M239.67 301.089C239.67 303.519 237.71 305.479 235.29 305.479C232.87 305.479 230.91 303.509 230.91 301.089C230.91 298.669 232.87 296.699 235.29 296.699C237.71 296.699 239.67 298.669 239.67 301.089Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 95,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M272.99 297.27H277.33C278.36 297.27 279.2 298.11 279.2 299.14V303.5C279.2 304.53 278.36 305.37 277.33 305.37H272.99C271.96 305.37 271.12 304.53 271.12 303.5V299.14C271.12 298.11 271.96 297.27 272.99 297.27Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 99,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M285.608 156.856C292.058 130.154 275.64 103.278 248.938 96.8279C222.235 90.3777 195.359 106.796 188.909 133.498C182.459 160.201 198.877 187.077 225.579 193.527C252.282 199.977 279.158 183.559 285.608 156.856Z",
                        fill: "#F5F5F5"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 103,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M239.48 167.091C239.47 169.361 237.62 171.191 235.35 171.181C233.08 171.171 231.25 169.321 231.26 167.051C231.27 164.781 233.12 162.951 235.39 162.961C237.66 162.971 239.49 164.821 239.48 167.091Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 107,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M227.15 161.81C231.66 157.07 239 156.93 243.63 161.59C245.73 163.7 249.01 160.45 246.91 158.34C240.56 151.94 230.13 151.98 223.89 158.53C221.84 160.68 225.09 163.97 227.14 161.81H227.15Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 111,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M213.19 142.668C219.44 137.268 226.9 133.868 235.26 133.848C243.54 133.828 251.71 136.768 257.77 142.468C259.94 144.498 263.23 141.258 261.05 139.218C254.1 132.698 244.79 129.208 235.28 129.238C225.67 129.258 217.12 133.188 209.94 139.398C207.68 141.348 210.95 144.618 213.19 142.678V142.668Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 115,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M218.7 153.278C222.89 148.748 228.76 145.908 234.97 145.768C241.34 145.618 247.5 148.248 251.93 152.798C254.01 154.938 257.29 151.678 255.21 149.548C249.97 144.158 242.53 140.968 234.99 141.148C227.61 141.318 220.45 144.578 215.45 149.998C213.43 152.178 216.68 155.468 218.7 153.278Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 119,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M274.194 133.987C275.407 127.129 270.831 120.586 263.973 119.373C257.115 118.16 250.572 122.737 249.359 129.594C248.146 136.452 252.722 142.995 259.58 144.208C266.438 145.421 272.981 140.845 274.194 133.987Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 123,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M266.979 128.258L265.19 126.469L257.178 134.48L258.967 136.269L266.979 128.258Z",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 127,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M258.96 126.481L257.171 128.27L265.182 136.281L266.971 134.492L258.96 126.481Z",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 131,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M233.91 58.2477C234.54 57.8677 235.21 57.5777 235.9 57.3777L236.31 55.4077L239.64 55.3477L240.12 57.3077C240.82 57.4777 241.5 57.7477 242.14 58.1077L243.82 57.0077L246.22 59.3277L245.18 61.0477C245.56 61.6777 245.85 62.3477 246.05 63.0377L248.02 63.4477L248.08 66.7777L246.12 67.2577C245.95 67.9577 245.68 68.6377 245.32 69.2777L246.42 70.9577L244.11 73.3577L242.39 72.3177C241.76 72.6977 241.09 72.9877 240.4 73.1877L239.99 75.1577L236.66 75.2177L236.18 73.2577C235.48 73.0877 234.8 72.8177 234.16 72.4577L232.48 73.5577L230.08 71.2377L231.12 69.5177C230.74 68.8877 230.45 68.2177 230.25 67.5277L228.28 67.1177L228.22 63.7877L230.18 63.3077C230.35 62.6077 230.62 61.9277 230.98 61.2877L229.88 59.6077L232.2 57.2077L233.92 58.2477H233.91ZM235.69 62.6877C234.32 64.1077 234.36 66.3677 235.78 67.7477C237.2 69.1177 239.46 69.0777 240.84 67.6577C242.21 66.2377 242.17 63.9777 240.75 62.5977C239.33 61.2277 237.07 61.2677 235.69 62.6877Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 135,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M191.63 65.7191C190.58 65.7191 189.25 65.6991 187.91 65.6691C186.32 65.6391 184.73 65.6091 183.62 65.6191C183.35 65.6191 183.12 65.3991 183.12 65.1291C183.12 64.8491 183.34 64.6291 183.61 64.6191C184.72 64.6091 186.33 64.6391 187.93 64.6691C189.52 64.6991 191.11 64.7291 192.22 64.7191C192.49 64.7191 192.72 64.9391 192.72 65.2091C192.72 65.4891 192.5 65.7091 192.23 65.7191C192.04 65.7191 191.84 65.7191 191.63 65.7191Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 139,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M186.66 69.0604C186.52 69.0604 186.39 69.0004 186.29 68.8904L183.24 65.4604C183.07 65.2704 183.07 64.9704 183.25 64.7804L186.22 61.6604C186.41 61.4604 186.73 61.4504 186.93 61.6404C187.13 61.8304 187.14 62.1504 186.95 62.3504L184.3 65.1404L187.04 68.2304C187.22 68.4404 187.2 68.7504 187 68.9404C186.9 69.0204 186.79 69.0704 186.67 69.0704L186.66 69.0604Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 143,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M289.73 62.7992C290.586 62.7992 291.28 62.1053 291.28 61.2492C291.28 60.3932 290.586 59.6992 289.73 59.6992C288.874 59.6992 288.18 60.3932 288.18 61.2492C288.18 62.1053 288.874 62.7992 289.73 62.7992Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 147,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M291.29 66.1398C291.29 66.9998 290.59 67.6898 289.74 67.6898C288.89 67.6898 288.19 66.9898 288.19 66.1398C288.19 65.2898 288.89 64.5898 289.74 64.5898C290.59 64.5898 291.29 65.2898 291.29 66.1398Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 151,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M289.73 72.5883C290.586 72.5883 291.28 71.8943 291.28 71.0383C291.28 70.1822 290.586 69.4883 289.73 69.4883C288.874 69.4883 288.18 70.1822 288.18 71.0383C288.18 71.8943 288.874 72.5883 289.73 72.5883Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 155,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M203.56 213.588C203.56 213.728 200.38 213.848 196.47 213.848C192.56 213.848 189.38 213.728 189.38 213.588C189.38 213.448 192.56 213.328 196.47 213.328C200.38 213.328 203.56 213.448 203.56 213.588Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 159,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M229.03 230.19C229.03 230.33 220.31 230.45 209.56 230.45C198.81 230.45 190.09 230.33 190.09 230.19C190.09 230.05 198.81 229.93 209.56 229.93C220.31 229.93 229.03 230.05 229.03 230.19Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 163,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M229.03 246.678C229.03 246.818 220.31 246.938 209.56 246.938C198.81 246.938 190.09 246.818 190.09 246.678C190.09 246.538 198.81 246.418 209.56 246.418C220.31 246.418 229.03 246.538 229.03 246.678Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 167,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M280.51 223.969H266.27C262.829 223.969 260.04 226.756 260.04 230.194C260.04 233.632 262.829 236.419 266.27 236.419H280.51C283.951 236.419 286.74 233.632 286.74 230.194C286.74 226.756 283.951 223.969 280.51 223.969Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 171,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M271.41 230.189C271.41 233.069 269.07 235.409 266.19 235.409C263.31 235.409 260.97 233.069 260.97 230.189C260.97 227.309 263.31 224.969 266.19 224.969C269.07 224.969 271.41 227.309 271.41 230.189Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 175,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M280.52 252.909H266.27C262.83 252.909 260.04 250.119 260.04 246.679C260.04 243.239 262.83 240.449 266.27 240.449H280.52C283.96 240.449 286.75 243.239 286.75 246.679C286.75 250.119 283.96 252.909 280.52 252.909Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 179,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M266.19 251.901C269.073 251.901 271.41 249.564 271.41 246.681C271.41 243.798 269.073 241.461 266.19 241.461C263.307 241.461 260.97 243.798 260.97 246.681C260.97 249.564 263.307 251.901 266.19 251.901Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 183,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M229.03 263.291C229.03 263.431 220.31 263.551 209.56 263.551C198.81 263.551 190.09 263.431 190.09 263.291C190.09 263.151 198.81 263.031 209.56 263.031C220.31 263.031 229.03 263.151 229.03 263.291Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 187,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M280.52 271.358H266.27C262.83 271.358 260.04 268.568 260.04 265.128C260.04 261.688 262.83 258.898 266.27 258.898H280.52C283.96 258.898 286.75 261.688 286.75 265.128C286.75 268.568 283.96 271.358 280.52 271.358Z",
                        fill: "#EBEBEB"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 191,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M266.19 270.35C269.073 270.35 271.41 268.013 271.41 265.13C271.41 262.247 269.073 259.91 266.19 259.91C263.307 259.91 260.97 262.247 260.97 265.13C260.97 268.013 263.307 270.35 266.19 270.35Z",
                        fill: "#E0E0E0"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 195,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M399.481 47.9669C400.915 39.0797 394.874 30.7122 385.987 29.2775C377.099 27.8429 368.732 33.8844 367.297 42.7716C365.863 51.6588 371.904 60.0263 380.791 61.4609C389.679 62.8956 398.046 56.854 399.481 47.9669Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 199,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M381.07 81.8111C380.98 81.8111 380.9 78.8011 380.9 75.0911C380.9 71.3811 380.98 68.3711 381.07 68.3711C381.16 68.3711 381.24 71.3811 381.24 75.0911C381.24 78.8011 381.16 81.8111 381.07 81.8111Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 203,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M371.68 64.5788C371.76 64.6288 370.23 67.4388 368.26 70.8488C366.29 74.2688 364.62 76.9988 364.54 76.9488C364.46 76.8988 365.99 74.0988 367.96 70.6788C369.93 67.2588 371.6 64.5288 371.68 64.5788Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 207,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M362.04 58.569C362.1 58.639 360.39 60.099 358.23 61.809C356.07 63.529 354.27 64.859 354.21 64.789C354.15 64.719 355.85 63.259 358.02 61.549C360.18 59.829 361.98 58.499 362.04 58.569Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 211,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M397.33 75.1914C397.33 75.1914 397.14 74.5614 396.9 73.5414C396.67 72.5114 396.33 71.1014 395.92 69.5514C395.5 68.0014 395.08 66.6114 394.77 65.6114C394.46 64.6014 394.27 63.9814 394.31 63.9614C394.35 63.9414 394.63 64.5414 395 65.5314C395.37 66.5214 395.83 67.9114 396.25 69.4614C396.67 71.0214 396.97 72.4514 397.14 73.4914C397.31 74.5314 397.38 75.1814 397.33 75.1914Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 215,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M413.86 63.4603C413.8 63.5303 411.64 61.7503 409.04 59.4803C406.44 57.2103 404.39 55.3103 404.45 55.2403C404.51 55.1703 406.67 56.9503 409.27 59.2203C411.87 61.4903 413.92 63.3903 413.86 63.4603Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 219,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M420.19 47.3411C420.19 47.3411 419.43 47.2611 418.22 47.0611C417.01 46.8611 415.34 46.6011 413.49 46.3911C411.64 46.1811 409.95 46.0511 408.73 45.9711C407.51 45.8911 406.75 45.8511 406.75 45.8011C406.75 45.7611 407.51 45.7111 408.74 45.7311C409.97 45.7511 411.67 45.8311 413.53 46.0511C415.39 46.2611 417.07 46.5611 418.27 46.8311C419.47 47.0911 420.2 47.3111 420.19 47.3511V47.3411Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 223,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M420.21 31.4914C420.23 31.5814 416.7 32.5214 412.33 33.5914C407.95 34.6614 404.39 35.4414 404.37 35.3514C404.35 35.2614 407.88 34.3214 412.25 33.2514C416.62 32.1814 420.19 31.4014 420.21 31.4914Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 227,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M401.79 13.0195C401.87 13.0695 400.18 15.7595 398.02 19.0295C395.86 22.2995 394.04 24.9095 393.96 24.8595C393.88 24.8095 395.57 22.1195 397.73 18.8495C399.89 15.5795 401.71 12.9695 401.79 13.0295V13.0195Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 231,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M383.52 9.16016C383.52 9.16016 383.56 10.0202 383.54 11.4002C383.52 12.7802 383.49 14.6902 383.5 16.7902C383.5 18.9002 383.53 20.8002 383.56 22.1802C383.58 23.5602 383.59 24.4102 383.54 24.4202C383.49 24.4202 383.4 23.5702 383.32 22.1902C383.23 20.8102 383.16 18.9002 383.16 16.7902C383.16 14.6802 383.22 12.7702 383.31 11.3902C383.39 10.0102 383.48 9.16016 383.53 9.16016H383.52Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 235,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M369.68 26.3988C369.6 26.4488 367.88 23.7888 365.85 20.4488C363.81 17.1188 362.23 14.3688 362.31 14.3288C362.39 14.2788 364.11 16.9388 366.14 20.2788C368.18 23.6088 369.76 26.3488 369.68 26.3988Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 239,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M361.41 33.4306C361.41 33.4306 360.74 33.3806 359.68 33.1606C358.62 32.9406 357.17 32.5806 355.58 32.1106C353.99 31.6406 352.58 31.1506 351.57 30.7606C350.56 30.3706 349.95 30.0906 349.97 30.0506C349.99 30.0006 350.63 30.2006 351.65 30.5306C352.68 30.8606 354.1 31.3106 355.68 31.7806C357.26 32.2506 358.7 32.6406 359.74 32.9206C360.78 33.2006 361.43 33.3806 361.42 33.4306H361.41Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 243,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M357.8 45.949C357.8 46.039 355.48 46.349 352.6 46.619C349.72 46.899 347.38 47.049 347.37 46.949C347.37 46.859 349.69 46.549 352.57 46.279C355.45 45.999 357.79 45.849 357.8 45.949Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 247,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M346.68 336.441C351.54 371.061 356.4 405.691 361.27 440.311L365.07 440.431C362.65 405.151 360.07 369.171 346.69 336.441H346.68Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 251,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M387.19 331.58C377 342.74 372.9 358.09 370.32 372.98C366.66 394.1 366.33 419.03 367.42 440.43L372.74 440.55C370.58 403.58 375.58 366.74 387.18 331.57L387.19 331.58Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 255,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M349.36 397.89C348.19 392.74 346.85 387.51 343.91 383.13C339.94 377.21 333.48 373.46 326.97 370.55C320.47 367.64 313.62 365.3 307.75 361.27C314.31 372.02 329.19 374.88 336.77 384.94C340.54 389.94 342.11 396.22 343.58 402.31C346.8 415.59 348.79 427.08 352.01 440.36H356.32C354.9 425.93 352.57 412.04 349.35 397.9L349.36 397.89Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 259,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M376.64 440.43C376.64 440.43 380.29 399.18 383 391.6C385.71 384.03 411.31 363.1 411.07 364.08C410.82 365.06 391.87 387.96 389.16 392.64C386.45 397.32 381.77 440.42 381.77 440.42H376.64V440.43Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 263,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M255.62 397.292C257.67 398.022 259.25 399.772 260.12 401.762C260.99 403.752 261.2 405.982 261.09 408.152C260.89 412.352 258.79 416.722 256.46 420.212C252.75 417.372 251.59 412.082 251.18 409.362C250.52 405.062 251.94 396.882 255.63 397.292",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 267,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M260.99 426.841C260.27 424.521 261.09 421.871 262.79 420.141C264.49 418.411 266.98 417.561 269.41 417.591C270.53 417.601 271.76 417.851 272.46 418.731C273.11 419.551 273.12 420.761 272.7 421.721C272.28 422.681 271.48 423.431 270.63 424.041C267.71 426.151 264.5 427.651 260.99 426.841Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 271,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M257.32 439.921C257.32 439.921 257.32 439.611 257.39 439.061C257.46 438.451 257.55 437.661 257.66 436.721C257.92 434.761 258.24 431.951 259.57 429.171C260.88 426.391 262.83 424.281 264.51 423.181C265.35 422.621 266.09 422.271 266.61 422.081C266.87 421.971 267.08 421.931 267.22 421.881C267.36 421.841 267.44 421.821 267.44 421.831C267.47 421.901 266.24 422.201 264.63 423.351C263.01 424.471 261.13 426.561 259.84 429.291C258.54 432.021 258.18 434.791 257.87 436.741C257.72 437.721 257.6 438.521 257.5 439.071C257.4 439.621 257.33 439.921 257.31 439.921H257.32Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 275,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M255.09 403.621C255.09 403.621 255.12 403.751 255.16 403.991C255.19 404.271 255.24 404.621 255.29 405.061C255.4 405.991 255.54 407.341 255.71 409.011C256.04 412.341 256.42 416.961 256.73 422.061C257.04 427.171 257.21 431.791 257.29 435.141C257.33 436.811 257.35 438.171 257.35 439.111C257.35 439.551 257.35 439.911 257.35 440.191C257.35 440.441 257.34 440.571 257.33 440.571C257.32 440.571 257.31 440.441 257.29 440.191C257.28 439.911 257.26 439.551 257.24 439.111C257.2 438.141 257.15 436.791 257.08 435.151C256.94 431.801 256.74 427.181 256.43 422.081C256.12 416.981 255.77 412.371 255.5 409.031C255.37 407.391 255.26 406.051 255.18 405.081C255.15 404.641 255.12 404.281 255.1 404.001C255.09 403.751 255.08 403.621 255.1 403.621H255.09Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 279,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M255.08 429.769C253.95 424.779 250.26 420.429 245.5 418.529C244.29 418.039 242.73 417.779 241.83 418.709C240.92 419.649 241.24 421.199 241.79 422.389C244.09 427.319 249.6 430.539 255.02 430.119",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 283,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M246.33 422.449C246.33 422.449 246.61 422.529 247.1 422.729C247.34 422.829 247.64 422.949 247.97 423.119C248.3 423.299 248.69 423.459 249.09 423.719C249.49 423.969 249.94 424.229 250.38 424.559C250.84 424.869 251.29 425.249 251.76 425.659C252.68 426.499 253.62 427.499 254.45 428.689C255.26 429.879 255.88 431.119 256.32 432.279C256.53 432.869 256.72 433.429 256.84 433.969C256.99 434.499 257.07 435.009 257.15 435.479C257.24 435.939 257.25 436.369 257.29 436.739C257.33 437.109 257.33 437.429 257.32 437.699C257.32 438.229 257.3 438.509 257.28 438.509C257.21 438.509 257.26 437.349 256.93 435.519C256.84 435.059 256.75 434.559 256.59 434.039C256.46 433.509 256.26 432.959 256.05 432.389C255.59 431.249 254.99 430.039 254.19 428.869C253.38 427.709 252.46 426.709 251.57 425.879C251.11 425.469 250.67 425.089 250.23 424.769C249.8 424.429 249.37 424.159 248.98 423.909C247.4 422.929 246.31 422.509 246.34 422.449H246.33Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 287,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M79.88 385.5L51.75 408.57L80.27 443.71C83.46 440.78 75.44 417.13 75.44 417.13L94.07 403.09L79.87 385.5H79.88Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 291,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: "0.6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M68.1501 405.032C67.4101 404.502 66.2701 404.651 65.7101 405.371C65.1501 406.091 65.2901 407.252 66.0201 407.792C66.7501 408.332 68.1301 408.192 68.6101 407.422C69.0901 406.652 68.8401 405.291 68.0101 404.931",
                            fill: "white"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                            lineNumber: 296,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 295,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: "0.6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M51.8599 408.481L54.6899 406.141L80.8299 440.981C80.8299 440.981 81.4099 443.101 80.1599 443.811L51.8599 408.481Z",
                            fill: "white"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                            lineNumber: 302,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 301,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M76.0898 417.561C75.9098 417.691 75.1398 416.901 73.8998 416.501C72.6698 416.071 71.5798 416.241 71.5198 416.031C71.4198 415.841 72.6598 415.271 74.1498 415.771C75.6398 416.261 76.2798 417.471 76.0898 417.561Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 307,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M77.5597 422.479C77.4297 422.649 76.5297 422.149 75.3297 422.149C74.1297 422.119 73.2297 422.609 73.0997 422.429C72.9397 422.289 73.8497 421.359 75.3397 421.379C76.8297 421.379 77.7197 422.339 77.5597 422.469V422.479Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 311,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M74.84 428.16C74.68 428.03 75.34 427.1 76.64 426.78C77.94 426.45 78.96 426.96 78.88 427.15C78.82 427.36 77.89 427.25 76.83 427.53C75.76 427.79 75 428.32 74.84 428.17V428.16Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 315,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M79.9798 413.892C79.7698 413.952 79.3598 412.972 78.5098 412.052C77.6798 411.122 76.7398 410.632 76.8098 410.422C76.8498 410.222 78.0898 410.442 79.0798 411.532C80.0798 412.622 80.1898 413.862 79.9798 413.892Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 319,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M83.4702 409.672C83.4002 409.732 82.7402 409.142 82.0502 407.902C81.7102 407.292 81.3702 406.512 81.1202 405.602C81.0002 405.152 80.9002 404.662 80.8402 404.152C80.8102 403.902 80.7602 403.632 80.8202 403.282C80.8402 402.942 81.2102 402.472 81.6702 402.462C82.0902 402.422 82.4602 402.642 82.6702 402.892C82.8902 403.142 83.0102 403.392 83.1102 403.622C83.3302 404.092 83.5002 404.562 83.6202 405.022C83.8602 405.942 83.9202 406.802 83.8702 407.512C83.7802 408.942 83.3602 409.742 83.2802 409.712C83.1602 409.682 83.3802 408.852 83.3302 407.512C83.3002 406.842 83.2002 406.062 82.9402 405.222C82.8102 404.802 82.6402 404.382 82.4302 403.962C82.2302 403.512 81.9702 403.192 81.7302 403.232C81.6402 403.252 81.6102 403.262 81.5602 403.432C81.5202 403.592 81.5502 403.822 81.5702 404.072C81.6202 404.552 81.7002 405.002 81.7902 405.432C81.9802 406.292 82.2502 407.042 82.5202 407.662C83.0602 408.892 83.5502 409.602 83.4502 409.672H83.4702Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 323,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M83.04 409.639C82.96 409.599 83.3 408.759 84.36 407.799C84.89 407.319 85.6 406.839 86.48 406.479C86.92 406.299 87.4 406.159 87.91 406.059C88.42 405.979 89.09 405.859 89.71 406.459C90.01 406.799 89.97 407.309 89.82 407.589C89.68 407.899 89.49 408.089 89.32 408.289C88.96 408.689 88.55 409.009 88.14 409.249C87.3 409.749 86.43 409.949 85.71 409.969C84.24 410.019 83.46 409.469 83.5 409.409C83.54 409.279 84.36 409.609 85.66 409.429C86.3 409.339 87.06 409.109 87.77 408.639C88.12 408.409 88.46 408.129 88.76 407.779C89.09 407.419 89.28 407.039 89.12 406.929C88.98 406.749 88.46 406.679 88.04 406.779C87.58 406.859 87.14 406.969 86.73 407.119C85.92 407.419 85.23 407.809 84.7 408.199C83.63 408.989 83.14 409.689 83.03 409.619L83.04 409.639Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 327,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M67.1299 404.35L67.4699 440.73L112.73 440.54C112.45 436.22 88.9899 427.64 88.9899 427.64L89.7299 404.32L67.1299 404.35Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 331,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: "0.6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M75.0001 425.73C74.1301 425.97 73.5201 426.96 73.7301 427.84C73.9401 428.72 74.9401 429.34 75.8101 429.12C76.6901 428.89 77.4501 427.73 77.1401 426.87C76.8401 426.01 75.6201 425.35 74.8201 425.78",
                            fill: "white"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                            lineNumber: 336,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 335,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: "0.6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M67.4699 440.58L67.4199 436.91L110.95 438.39C110.95 438.39 112.96 439.27 112.73 440.69L67.4699 440.59V440.58Z",
                            fill: "white"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                            lineNumber: 342,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 341,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M89.7298 427.4C89.7298 427.62 88.6198 427.73 87.5298 428.44C86.4298 429.13 85.8698 430.09 85.6698 430C85.4698 429.96 85.7898 428.64 87.1198 427.79C88.4398 426.94 89.7798 427.19 89.7298 427.4Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 347,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M94.4902 429.339C94.5402 429.549 93.5902 429.929 92.8402 430.869C92.0702 431.789 91.8802 432.799 91.6602 432.789C91.4502 432.829 91.2902 431.529 92.2402 430.379C93.1802 429.219 94.4802 429.129 94.4902 429.339Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 351,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M97.2097 435.01C97.0097 435.06 96.6997 433.95 97.2597 432.74C97.8097 431.52 98.8497 431.04 98.9497 431.22C99.0797 431.4 98.4097 432.05 97.9597 433.06C97.4897 434.05 97.4297 434.98 97.2097 435.01Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 355,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M89.31 422.061C89.22 422.261 88.2 421.961 86.95 422.061C85.7 422.121 84.73 422.551 84.61 422.361C84.48 422.201 85.43 421.381 86.9 421.291C88.37 421.191 89.41 421.891 89.3 422.071L89.31 422.061Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 359,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M88.2098 416.701C88.2098 416.791 87.3398 416.931 85.9398 416.701C85.2498 416.581 84.4298 416.361 83.5698 415.981C83.1398 415.791 82.6998 415.561 82.2698 415.291C82.0598 415.161 81.8098 415.021 81.5798 414.761C81.3298 414.531 81.1898 413.941 81.4698 413.581C81.6998 413.231 82.1098 413.081 82.4398 413.071C82.7698 413.061 83.0398 413.121 83.2898 413.181C83.7898 413.301 84.2698 413.471 84.6998 413.661C85.5698 414.051 86.2698 414.541 86.7998 415.021C87.8498 415.991 88.2198 416.811 88.1398 416.861C88.0398 416.931 87.5298 416.241 86.4498 415.451C85.9198 415.051 85.2298 414.641 84.4198 414.321C84.0098 414.161 83.5798 414.021 83.1198 413.921C82.6398 413.801 82.2298 413.791 82.1098 414.011C82.0698 414.091 82.0598 414.121 82.1598 414.261C82.2598 414.391 82.4598 414.511 82.6598 414.651C83.0598 414.911 83.4698 415.131 83.8598 415.331C84.6498 415.721 85.4098 415.981 86.0598 416.161C87.3598 416.521 88.2098 416.571 88.2098 416.691V416.701Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 363,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M87.9197 417.021C87.8397 417.051 87.3997 416.261 87.3097 414.831C87.2697 414.121 87.3397 413.261 87.6097 412.351C87.7497 411.901 87.9297 411.431 88.1797 410.971C88.4397 410.521 88.7597 409.921 89.6197 409.821C90.0697 409.801 90.4397 410.151 90.5697 410.451C90.7197 410.761 90.7497 411.021 90.8097 411.281C90.8997 411.811 90.8897 412.331 90.8097 412.811C90.6697 413.771 90.2797 414.581 89.8497 415.161C88.9697 416.341 88.0497 416.601 88.0297 416.531C87.9597 416.421 88.7197 415.991 89.3997 414.861C89.7297 414.301 90.0197 413.571 90.1097 412.731C90.1497 412.311 90.1397 411.871 90.0597 411.421C89.9897 410.941 89.7997 410.551 89.6197 410.601C89.3997 410.601 89.0097 410.961 88.8297 411.351C88.5997 411.761 88.4197 412.171 88.2697 412.581C87.9897 413.401 87.8697 414.181 87.8397 414.841C87.7797 416.171 88.0197 417.001 87.8997 417.031L87.9197 417.021Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 367,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M71.01 193.609C71.01 193.609 64.67 200.399 61.55 206.909C58.43 213.419 56.44 228.949 56.44 228.949C56.44 228.949 47.68 248.459 49.01 258.439C50.34 268.419 59.26 336.689 59.26 336.689C59.26 336.689 58.82 403.049 65.14 414.889L66.55 421.279L92.07 416.749L91.91 412.919L97.94 282.799L113.29 194.209L71.01 193.619V193.609Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 371,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M127.32 212.961C127.32 212.961 142.58 296.951 143.92 311.531C145.27 326.111 93.3398 402.971 93.3398 402.971L69.5898 378.241L103.67 314.891L92.5898 251.051L108 194.641L124.18 195.211L127.31 212.961H127.32Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 375,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M61.1899 394.232C61.1899 394.232 61.1999 394.872 61.3699 396.092C61.5299 397.312 61.8299 399.102 62.2499 401.412C63.0999 406.032 64.4699 412.732 66.3199 421.112L66.3599 421.282L66.5399 421.252C73.7499 419.992 82.4099 418.472 92.0699 416.782L92.2599 416.752V416.552C92.9799 399.042 93.8299 378.102 94.7399 355.662C95.6799 331.512 96.5599 309.112 97.2699 290.742V290.762C99.1799 276.002 100.76 263.792 101.87 255.252C102.4 251.012 102.82 247.682 103.11 245.382C103.24 244.262 103.35 243.402 103.42 242.792C103.49 242.202 103.5 241.902 103.5 241.902C103.5 241.902 103.44 242.202 103.35 242.782C103.26 243.392 103.14 244.252 102.97 245.362C102.65 247.652 102.19 250.982 101.6 255.212C100.44 263.742 98.7899 275.942 96.7999 290.692V290.712C96.0499 309.082 95.1499 331.482 94.1699 355.632C93.2899 378.082 92.4799 399.012 91.7899 416.532L91.9899 416.302C82.3399 418.032 73.6899 419.592 66.4799 420.892L66.6899 421.032C64.7899 412.672 63.3599 405.982 62.4499 401.382C61.9999 399.082 61.6599 397.292 61.4799 396.082C61.3699 395.482 61.3199 395.012 61.2699 394.702C61.2299 394.392 61.1899 394.232 61.1899 394.232Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 379,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M31.5601 158.851C31.5601 158.851 47.3601 124.791 51.0901 122.981C54.8201 121.171 65.5401 120.571 66.9901 122.601L38.6701 163.381L31.5601 158.851Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 383,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M32.3799 154.251C31.6799 153.871 30.8599 153.761 30.0899 153.951H30.0699C25.1699 155.181 7.21993 159.991 5.21993 165.081C2.89993 170.991 17.0899 247.791 18.9199 249.871C20.7499 251.951 48.9699 248.451 49.8299 245.181C50.6899 241.901 40.6499 159.121 40.6499 159.121L32.3799 154.231V154.251Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 387,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M10.1098 206.57C10.1098 206.57 8.50981 206.66 6.91981 210.2C5.32981 213.74 8.12981 242.39 10.2398 245.32C12.3498 248.25 18.4998 248.87 18.4998 248.87L10.1098 206.57Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 391,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M41.5598 246.753C41.5598 246.753 41.4998 246.692 41.4198 246.562C41.3198 246.402 41.1998 246.213 41.0498 245.973C40.7198 245.453 40.2798 244.663 39.7598 243.613C38.7198 241.523 37.4798 238.353 36.5398 234.283C35.5798 230.213 34.8798 225.283 34.2998 219.812C33.7198 214.333 33.0698 208.313 32.3998 201.993C31.0398 189.353 29.8998 177.882 29.7198 169.542C29.6198 165.372 29.6998 162.003 29.8298 159.673C29.8898 158.513 29.9598 157.603 30.0198 156.993C30.0498 156.713 30.0698 156.483 30.0898 156.303C30.1098 156.143 30.1198 156.062 30.1298 156.062C30.1298 156.062 30.1398 156.143 30.1398 156.303C30.1398 156.493 30.1298 156.723 30.1198 157.003C30.0998 157.663 30.0698 158.552 30.0298 159.682C29.9698 162.012 29.9398 165.373 30.0898 169.533C30.3698 177.853 31.5598 189.303 32.9198 201.943C33.5798 208.263 34.2098 214.272 34.7798 219.762C35.3298 225.243 35.9898 230.153 36.8998 234.213C37.7898 238.273 38.9698 241.432 39.9498 243.542C40.4398 244.592 40.8498 245.402 41.1498 245.932C41.2798 246.182 41.3898 246.382 41.4798 246.542C41.5498 246.682 41.5798 246.752 41.5798 246.762L41.5598 246.753Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 395,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M32.5498 201.202C32.5498 201.282 31.2698 201.472 29.2098 201.832C27.1398 202.192 24.2998 202.742 21.1998 203.502C18.0998 204.272 15.3198 205.112 13.3298 205.762C11.3298 206.412 10.1098 206.842 10.0798 206.762C10.0698 206.732 10.3598 206.582 10.8998 206.342C11.4398 206.102 12.2298 205.782 13.2098 205.412C15.1798 204.672 17.9498 203.772 21.0798 202.992C24.1998 202.222 27.0798 201.732 29.1598 201.462C30.1998 201.332 31.0498 201.242 31.6398 201.202C32.2298 201.162 32.5498 201.162 32.5598 201.202H32.5498Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 399,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M9.73985 203.391C9.73985 203.391 9.79985 203.551 9.85985 203.861C9.92985 204.231 10.0098 204.681 10.0998 205.221C10.3098 206.491 10.5898 208.181 10.9398 210.241C11.6498 214.471 12.6798 220.321 13.9598 226.741C15.2498 233.161 16.5598 238.951 17.5398 243.131C18.0198 245.161 18.4098 246.831 18.7098 248.081C18.8298 248.621 18.9298 249.061 19.0098 249.431C19.0698 249.741 19.0998 249.911 19.0798 249.911C19.0598 249.911 18.9998 249.761 18.9098 249.461C18.7998 249.101 18.6698 248.661 18.5098 248.131C18.1698 246.981 17.7198 245.301 17.1798 243.221C16.1098 239.061 14.7398 233.281 13.4398 226.851C12.1498 220.421 11.1798 214.561 10.5598 210.311C10.2498 208.181 10.0198 206.461 9.88985 205.271C9.82985 204.721 9.77985 204.271 9.73985 203.891C9.70985 203.571 9.70985 203.411 9.71985 203.411L9.73985 203.391Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 403,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M29.8301 168.822C29.7601 168.822 29.8001 168.002 29.9901 166.692C30.1701 165.392 30.5101 163.602 31.0201 161.652C31.5301 159.712 32.1301 157.982 32.6101 156.762C33.0901 155.542 33.4601 154.812 33.5201 154.832C33.6601 154.892 32.5301 157.932 31.5201 161.782C30.4901 165.632 29.9801 168.842 29.8201 168.812L29.8301 168.822Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 407,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M128.95 95.891C128.79 93.001 128.42 78.931 128.26 76.801C128.03 73.911 126.84 71.041 124.72 69.061C122.6 67.091 119.3 66.241 116.7 67.511C114.98 67.941 113.63 69.281 113.15 70.991C107.23 92.041 108.16 114.451 105.55 136.221C113.28 137.481 123.68 136.961 131.49 136.551C131.49 119.171 130.02 114.651 128.95 95.891Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 411,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M125.69 125.569C125.69 125.569 125.65 125.379 125.63 125.009C125.61 124.589 125.59 124.049 125.55 123.399C125.5 121.929 125.44 119.919 125.37 117.459C125.26 112.379 125.12 105.489 124.96 97.869C124.9 94.049 124.85 90.399 124.79 87.079C124.77 85.429 124.75 83.8491 124.73 82.3691C124.72 80.899 124.68 79.529 124.45 78.319C124.05 75.849 122.64 74.219 121.44 73.559C120.23 72.879 119.39 73.009 119.4 72.939C119.4 72.939 119.45 72.919 119.54 72.919C119.64 72.919 119.78 72.899 119.96 72.919C120.33 72.949 120.89 73.059 121.52 73.389C122.15 73.719 122.86 74.269 123.47 75.089C124.08 75.899 124.54 76.999 124.79 78.249C125.05 79.499 125.11 80.889 125.14 82.359C125.17 83.839 125.21 85.409 125.24 87.069C125.3 90.389 125.37 94.029 125.44 97.859C125.54 105.479 125.63 112.379 125.7 117.449C125.7 119.909 125.72 121.919 125.72 123.389C125.72 124.049 125.71 124.579 125.7 125.009C125.69 125.379 125.68 125.569 125.66 125.569H125.69Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 415,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M118.67 117.592C118.67 117.592 118.6 117.142 118.58 116.322C118.55 115.502 118.53 114.322 118.53 112.862C118.53 109.942 118.59 105.912 118.7 101.452C118.82 97.052 118.93 93.072 119.02 90.052C119.07 88.672 119.1 87.512 119.13 86.602C119.16 85.782 119.2 85.332 119.23 85.332C119.26 85.332 119.3 85.782 119.32 86.602C119.35 87.422 119.37 88.602 119.37 90.062C119.37 92.982 119.31 97.012 119.2 101.472C119.08 105.872 118.97 109.852 118.88 112.872C118.83 114.252 118.8 115.412 118.77 116.322C118.74 117.142 118.7 117.592 118.67 117.592Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 419,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M67.7399 122.98C66.7299 122.98 47.8799 124.33 43.4999 144.52C39.1199 164.72 37.8599 193.02 37.8599 193.02L58.3099 190.97L67.7299 122.98H67.7399Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 423,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M77.01 118.512L80.34 112.382L109.29 111.602L111.25 116.822L128.34 122.982L127 198.722L139.65 268.622C139.65 268.622 139.52 278.682 105.78 281.912L103.11 245.372L86.26 244.972L86.72 280.662C86.72 280.662 56.98 280.182 47.55 273.452C47.55 273.452 44.69 239.442 52.81 214.962C59.92 193.522 62.69 187.982 62.69 187.982L63.3 123.722L77.01 118.522V118.512Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 427,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: "0.3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M68.0498 123.852C62.2198 148.802 56.3798 173.742 50.5498 198.692L56.4798 204.982C62.5798 202.262 66.7698 195.732 66.7098 189.062C66.6798 185.602 65.6198 182.232 65.4698 178.772C65.1498 170.972 69.4598 163.822 71.4298 156.272C74.2198 145.572 72.2098 134.102 68.0498 123.862V123.852Z",
                            fill: "black"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                            lineNumber: 432,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 431,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M124.71 122.022C131.67 123.302 137.11 130.712 138.69 138.652C139.61 143.302 142.32 165.722 142.32 165.722L143.81 173.932C143.81 173.932 145.06 202.192 139.07 205.342C132.07 209.022 128.06 204.982 125.75 202.022C123.45 199.062 120.97 179.802 120.97 179.802L123.6 133.722L124.71 122.012V122.022Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 437,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M128.03 133.352C128.03 133.352 128.02 133.632 127.95 134.162C127.87 134.742 127.76 135.502 127.62 136.472C127.3 138.522 126.86 141.412 126.31 144.952C125.73 148.542 125.05 152.802 124.3 157.512C123.91 159.882 123.51 162.362 123.09 164.932C122.66 167.502 122.15 170.152 121.93 172.892C121.79 174.512 121.78 176.112 121.91 177.612V177.582C124.07 187.282 126.1 195.862 127.63 202.002C128.4 205.032 129.03 207.482 129.48 209.222C129.69 210.032 129.86 210.682 129.99 211.172C130.1 211.622 130.15 211.852 130.14 211.862C130.12 211.862 130.04 211.642 129.9 211.202C129.75 210.722 129.55 210.082 129.3 209.282C128.8 207.602 128.11 205.152 127.3 202.092C125.69 195.972 123.59 187.402 121.41 177.702V177.682V177.662C121.28 176.122 121.29 174.492 121.43 172.852C121.65 170.072 122.17 167.422 122.6 164.852C123.03 162.282 123.44 159.802 123.84 157.442C124.64 152.732 125.36 148.482 125.96 144.902C126.58 141.372 127.08 138.502 127.44 136.452C127.62 135.492 127.76 134.732 127.87 134.162C127.97 133.642 128.04 133.362 128.05 133.372L128.03 133.352Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 441,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M53.79 127.74L59.1 198.66C59.1 198.66 69.02 153.44 69.43 141.87C69.84 130.3 67.42 122.15 67.42 122.15C67.42 122.15 57.8 120.64 53.79 127.74Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 445,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M50.75 123.211C50.75 123.211 51.5 123.421 52.52 124.181C53.03 124.571 53.49 125.251 53.76 126.051C54.04 126.841 54.21 127.751 54.29 128.691C54.46 130.581 54.45 132.271 54.45 133.501C54.45 134.731 54.45 135.481 54.37 135.491C54.3 135.491 54.17 134.741 54.08 133.511C53.98 132.281 53.93 130.591 53.77 128.741C53.61 126.911 53.19 125.231 52.28 124.461C51.39 123.681 50.7 123.291 50.76 123.211H50.75Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 449,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M87.5298 125.78C87.5298 125.78 87.2998 125.58 86.9298 125.16C86.5598 124.74 86.0398 124.11 85.3898 123.31L85.6898 123.27C84.9698 125.36 83.8798 128.39 82.6198 131.79L82.4398 132.27L82.1698 131.86C82.0198 131.64 81.8498 131.41 81.6998 131.16C80.7498 129.56 80.1698 127.92 79.6298 126.44C79.0998 124.96 78.6698 123.61 78.2998 122.48C77.9298 121.35 77.6198 120.44 77.3898 119.82C77.1698 119.2 77.0298 118.85 77.0698 118.84C77.0998 118.83 77.2898 119.14 77.5798 119.75C77.8698 120.35 78.2298 121.25 78.6498 122.36C79.0698 123.52 79.5598 124.83 80.0898 126.28C80.6498 127.74 81.2498 129.37 82.1598 130.89C82.2998 131.13 82.4598 131.34 82.5998 131.55L82.1498 131.62C83.4198 128.23 84.5698 125.22 85.3898 123.16L85.4998 122.87L85.6798 123.12C86.8898 124.75 87.5898 125.75 87.5398 125.8L87.5298 125.78Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 453,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M110.53 120.902C110.53 120.902 110.44 121.252 110.18 121.852C109.92 122.452 109.53 123.302 109.02 124.352C108.51 125.402 107.9 126.642 107.17 128.002C106.43 129.362 105.64 130.862 104.55 132.332L104.22 132.782L104.09 132.232C104.07 132.162 104.05 132.082 104.04 132.002C103.22 128.512 102.51 125.342 102.02 123.032L102.34 123.112C101.49 124.052 100.56 124.452 100.04 124.862C99.5001 125.252 99.2501 125.532 99.2201 125.512C99.2001 125.492 99.4001 125.162 99.9101 124.702C100.41 124.232 101.31 123.772 102.06 122.872L102.29 122.592L102.38 122.952C102.96 125.242 103.73 128.402 104.55 131.892C104.57 131.972 104.59 132.052 104.6 132.122L104.14 132.022C105.18 130.622 106 129.122 106.75 127.792C107.47 126.472 108.12 125.262 108.69 124.202C109.2 123.262 109.63 122.452 109.99 121.772C110.3 121.202 110.49 120.892 110.52 120.912L110.53 120.902Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 457,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M88.03 125.489C88.03 125.489 85.22 161.989 84.49 166.689C83.76 171.379 86.26 244.969 86.26 244.969L102.7 246.019C102.7 246.019 102.87 185.269 102.7 184.849C102.53 184.429 102.78 141.959 102.78 141.959L99.05 125.039L92.69 132.569L88.03 125.489Z",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 461,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M106.38 160.19C106.42 160.27 105.8 160.4 105.51 161.19C105.37 161.56 105.33 162.09 105.59 162.51C105.83 162.94 106.41 163.16 107 163.05C107.59 162.94 108.05 162.53 108.12 162.04C108.21 161.55 107.98 161.08 107.72 160.78C107.16 160.15 106.54 160.24 106.55 160.15C106.55 160.12 106.68 160.05 106.95 160.06C107.22 160.06 107.62 160.19 107.98 160.52C108.33 160.85 108.68 161.41 108.59 162.11C108.52 162.83 107.84 163.43 107.09 163.56C106.34 163.71 105.49 163.39 105.17 162.74C104.84 162.11 104.97 161.47 105.17 161.04C105.39 160.6 105.72 160.34 105.97 160.24C106.22 160.14 106.37 160.16 106.38 160.18V160.19Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 465,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M106.38 192.061C106.42 192.141 105.8 192.271 105.51 193.061C105.37 193.431 105.33 193.961 105.59 194.381C105.83 194.811 106.41 195.031 107 194.921C107.59 194.811 108.05 194.401 108.12 193.911C108.21 193.421 107.98 192.951 107.72 192.651C107.16 192.021 106.54 192.111 106.55 192.021C106.55 191.991 106.68 191.921 106.95 191.931C107.22 191.931 107.62 192.061 107.98 192.391C108.33 192.721 108.68 193.281 108.59 193.981C108.52 194.701 107.84 195.301 107.09 195.431C106.34 195.581 105.49 195.261 105.17 194.611C104.84 193.981 104.97 193.341 105.17 192.911C105.39 192.471 105.72 192.211 105.97 192.111C106.22 192.011 106.37 192.031 106.38 192.051V192.061Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 469,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M107.83 230.6C107.87 230.68 107.25 230.81 106.96 231.6C106.82 231.97 106.78 232.5 107.04 232.92C107.28 233.35 107.86 233.57 108.45 233.46C109.04 233.35 109.5 232.94 109.57 232.45C109.66 231.96 109.43 231.49 109.17 231.19C108.61 230.56 107.99 230.65 108 230.56C108 230.53 108.13 230.46 108.4 230.47C108.67 230.47 109.07 230.6 109.43 230.93C109.78 231.26 110.13 231.82 110.04 232.52C109.97 233.24 109.29 233.84 108.54 233.97C107.79 234.12 106.94 233.8 106.62 233.15C106.29 232.52 106.42 231.88 106.62 231.45C106.84 231.01 107.17 230.75 107.42 230.65C107.67 230.55 107.82 230.57 107.83 230.59V230.6Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 473,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                        opacity: "0.3",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            d: "M129.15 159.91C128.79 157 128.02 154.14 127.82 151.22C127.59 147.72 128.19 144.17 127.51 140.73C125.13 144.7 124.59 149.5 124.58 154.13C124.56 158.76 125 163.42 124.35 168C123.9 171.13 122.88 174.91 122.5 178.05C122.12 181.19 123.02 182.94 123.94 187.66C127.11 178.7 130.32 169.35 129.16 159.92L129.15 159.91Z",
                            fill: "black"
                        }, void 0, false, {
                            fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                            lineNumber: 478,
                            columnNumber: 6
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 477,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M123.6 152.469C127.45 163.199 125.88 175.729 119.48 185.169L123.6 152.469Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 483,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M119.49 185.159C119.4 185.099 120.5 183.519 121.81 180.739C122.48 179.349 123.14 177.639 123.75 175.709C124.33 173.759 124.83 171.589 125.13 169.259C125.41 166.929 125.46 164.699 125.39 162.669C125.28 160.639 125.06 158.819 124.76 157.319C124.18 154.299 123.51 152.499 123.61 152.469C123.64 152.459 123.82 152.899 124.12 153.719C124.27 154.129 124.45 154.629 124.6 155.219C124.76 155.809 124.98 156.489 125.12 157.249C125.47 158.759 125.73 160.599 125.87 162.649C125.97 164.699 125.94 166.969 125.65 169.329C125.34 171.689 124.82 173.889 124.21 175.859C123.57 177.809 122.86 179.529 122.14 180.909C121.81 181.619 121.44 182.209 121.14 182.749C120.84 183.289 120.55 183.729 120.3 184.089C119.81 184.809 119.53 185.189 119.5 185.169L119.49 185.159Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 487,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M73.9299 200.729C73.8899 200.779 73.1299 200.209 72.0099 199.179C70.8799 198.149 69.3999 196.639 67.8999 194.849C66.4099 193.059 65.1899 191.329 64.3799 190.039C63.5699 188.749 63.1399 187.899 63.1999 187.869C63.2699 187.829 63.7999 188.599 64.6899 189.829C65.5799 191.059 66.8299 192.739 68.3099 194.519C69.7899 196.289 71.2199 197.829 72.2699 198.919C73.3199 200.009 73.9899 200.679 73.9399 200.739L73.9299 200.729Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 491,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M67.34 201.759C67.28 201.789 66.87 201.079 66.32 199.869C65.77 198.669 65.1 196.959 64.51 195.029C63.92 193.099 63.53 191.309 63.31 190.009C63.09 188.709 63.03 187.889 63.1 187.879C63.25 187.849 63.83 191.059 65.01 194.879C66.17 198.709 67.48 201.689 67.33 201.759H67.34Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 495,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M99.4997 182.72C99.4497 182.79 98.8897 182.4 97.9097 182.01C96.9297 181.61 95.4697 181.29 93.8597 181.4C92.2497 181.52 90.8497 182.05 89.9397 182.58C89.0197 183.11 88.5297 183.57 88.4597 183.51C88.4197 183.48 88.8297 182.91 89.7397 182.27C90.6497 181.63 92.1097 181.01 93.8197 180.88C95.5297 180.76 97.0697 181.17 98.0597 181.67C99.0597 182.17 99.5397 182.68 99.4997 182.72Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 499,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M43.9799 180.34C43.9799 180.34 36.6399 188.33 37.1999 193.91C37.7599 199.49 40.3599 211.77 47.2499 211.4C54.1299 211.03 60.3599 199.96 61.2899 191.18C62.2199 182.4 61.2899 161.75 61.2899 161.75L51.4899 163.96L43.9899 180.34H43.9799Z",
                        fill: "#FFBE9D"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 503,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M29.3898 129.129C34.4698 135.619 47.0598 151.269 47.0598 151.269L58.9098 148.259C59.7698 148.039 60.4098 147.319 60.5198 146.429C60.5898 145.859 60.4298 145.279 60.0698 144.819L43.4698 123.799C42.1898 122.189 40.0198 121.579 38.0998 122.299L30.5098 125.149C28.8898 125.759 28.3298 127.759 29.3898 129.119V129.129Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 507,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M59.26 145.61C59.26 145.61 58.83 145.24 58.12 144.52C57.42 143.79 56.41 142.72 55.2 141.36C52.78 138.65 49.54 134.81 46.21 130.36C45.38 129.25 44.58 128.16 43.82 127.09C43.07 126.03 42.33 125 41.34 124.45C40.35 123.87 39.21 124.11 38.23 124.46C37.23 124.8 36.29 125.12 35.42 125.42C33.76 125.97 32.39 126.42 31.34 126.77C30.37 127.08 29.83 127.23 29.82 127.2C29.81 127.17 30.33 126.95 31.27 126.58C32.31 126.19 33.66 125.68 35.3 125.07C36.16 124.76 37.1 124.42 38.09 124.05C38.6 123.87 39.15 123.7 39.75 123.65C40.35 123.59 41.01 123.73 41.57 124.03C42.71 124.65 43.48 125.75 44.24 126.8C45.01 127.85 45.81 128.94 46.63 130.05C49.95 134.48 53.13 138.35 55.48 141.12C56.61 142.44 57.56 143.54 58.28 144.39C58.94 145.16 59.3 145.6 59.27 145.62L59.26 145.61Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 511,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M36.1099 126.951C36.5799 126.721 37.2099 126.941 37.4399 127.401C37.6699 127.871 37.4499 128.501 36.9899 128.731C36.5199 128.961 35.8899 128.741 35.6599 128.281C35.4299 127.811 35.6499 127.181 36.1099 126.951Z",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 515,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M46.28 141.139L40.8 142.049L38.78 143.839C38.22 144.339 37.92 145.089 38.02 145.829C38.12 146.549 38.43 147.479 39.26 148.489C40.57 150.069 41.64 150.869 41.64 150.869C41.64 150.869 39.71 153.989 42.7 155.569C42.7 155.569 42.5 158.809 45.63 159.139C45.63 159.139 46.54 162.209 48.89 162.379L51.33 164.789L50.59 166.269L61.46 166.599C61.46 166.599 61.54 160.639 61.03 157.919C60.54 155.309 60.18 154.479 60.18 154.479C60.73 151.409 58.44 150.139 58.1 150.349C58.1 150.349 63.29 148.989 62.62 145.419L57.64 146.699L48.51 148.599L54.36 144.679C54.65 144.409 54.92 144.109 55.13 143.779C55.63 142.989 56.37 141.559 55.74 140.729C55.47 140.369 48.27 143.379 48.27 143.379L43.85 147.509L42.46 145.529C42.46 145.529 47.01 144.569 46.29 141.139H46.28Z",
                        fill: "#FFBE9D"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 519,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M58.8 150.412C58.82 150.562 56.87 150.842 54.56 151.522C52.24 152.182 50.45 152.992 50.38 152.852C50.31 152.732 52.04 151.702 54.41 151.022C56.78 150.332 58.79 150.282 58.8 150.412Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 523,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M56.2901 159.821C56.2301 159.751 57.1401 159.211 58.1101 157.881L58.2901 157.631L58.4201 157.921C58.5801 158.271 58.8901 158.591 59.3101 158.771C59.7201 158.941 60.2701 158.971 60.6201 158.691C60.7901 158.551 60.8501 158.391 60.8101 158.121C60.7601 157.831 60.7101 157.531 60.6601 157.221C60.5401 156.611 60.4901 155.931 60.2901 155.381C60.0901 154.821 59.6301 154.511 59.0701 154.511C58.5101 154.491 57.9601 154.681 57.4101 154.821C56.3501 155.081 55.3701 155.321 54.4701 155.541C52.8001 155.941 51.7601 156.141 51.7501 156.071C51.7401 156.001 52.7501 155.661 54.3901 155.181C55.2701 154.931 56.2301 154.651 57.2701 154.361C57.8101 154.211 58.4001 153.981 59.0901 154.001C59.4301 154.001 59.8001 154.091 60.1201 154.311C60.4401 154.531 60.6501 154.861 60.7801 155.201C61.0301 155.901 61.0601 156.531 61.1701 157.151C61.2201 157.461 61.2601 157.771 61.3101 158.071C61.3801 158.391 61.2301 158.881 60.9301 159.071C60.3401 159.491 59.6401 159.391 59.1501 159.161C58.6401 158.921 58.2701 158.501 58.1001 158.061L58.4101 158.101C57.8701 158.771 57.3401 159.211 56.9501 159.481C56.5601 159.751 56.3201 159.851 56.3001 159.821H56.2901Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 527,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M46.69 144.731C46.8 144.831 45.62 146.191 44.25 147.921C42.88 149.651 41.83 151.101 41.7 151.021C41.58 150.951 42.45 149.341 43.84 147.591C45.23 145.831 46.59 144.621 46.69 144.721V144.731Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 531,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M48.9401 148.48C48.9901 148.62 47.8001 148.98 46.9801 150.16C46.1201 151.31 46.1501 152.55 46.0001 152.55C45.9401 152.55 45.8301 152.25 45.8601 151.73C45.8901 151.22 46.0901 150.5 46.5601 149.85C47.0301 149.2 47.6401 148.78 48.1201 148.6C48.6001 148.41 48.9301 148.42 48.9401 148.48Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 535,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M107.61 67.9109C106.77 65.7109 107.1 63.2609 106.77 60.9209C106.04 55.7909 101.91 51.6709 97.1297 49.6609C92.6797 47.7909 87.2497 48.1909 81.8897 49.9209C78.2097 51.1009 74.7497 53.1709 72.2997 56.1509C69.0397 60.1109 67.8897 65.7009 69.3197 70.6209L95.5797 66.8309C98.0397 68.8709 100.55 70.9409 103.53 72.0809C104.4 72.4109 105.35 72.6709 106.27 72.5209C107.19 72.3709 108.09 71.7509 108.31 70.8409C108.55 69.8509 107.96 68.8709 107.6 67.9109H107.61Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 539,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M91.29 48.3506C97.04 47.3806 102.09 49.6006 106.93 52.8506C111.77 56.1006 115.42 60.9906 117.52 66.4306C119.73 72.1606 120.29 80.4406 118.57 86.3406C116.86 92.2406 112.69 96.5406 107.31 98.4406C99.4 101.231 90.27 100.891 83.59 95.8306C76.91 90.7706 73.09 82.4006 72.78 74.0206C72.57 68.2506 73.95 62.3406 77.19 57.5506C80.42 52.7606 85.57 49.1906 91.29 48.3506Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 543,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M82.1599 110.871L82.3699 115.551L92.6799 133.801L107.72 114.591L108.23 66.6409C108.19 64.4209 106.46 62.6009 104.24 62.4409L81.3199 57.5509C74.0199 57.0509 69.5199 66.1909 69.3399 73.5009C69.1299 81.6309 69.1999 91.5509 70.3899 97.5709C72.7799 109.661 82.1599 110.861 82.1599 110.861",
                        fill: "#FFBE9D"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 547,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M72.6201 83.599C72.6201 84.429 73.3201 85.099 74.1801 85.119C75.0401 85.129 75.7401 84.479 75.7401 83.649C75.7401 82.829 75.0401 82.149 74.1801 82.129C73.3201 82.119 72.6201 82.769 72.6201 83.599Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 551,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M71.6802 78.2024C71.8802 78.4024 73.0302 77.4824 74.7102 77.4224C76.3902 77.3424 77.6502 78.1524 77.8202 77.9424C77.9102 77.8524 77.7002 77.4924 77.1402 77.1224C76.6002 76.7524 75.6802 76.4124 74.6402 76.4524C73.5902 76.5024 72.7302 76.9124 72.2402 77.3324C71.7402 77.7524 71.5802 78.1224 71.6802 78.2124V78.2024Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 555,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M87.8599 83.31C87.8599 84.13 88.5599 84.81 89.4199 84.83C90.2799 84.84 90.9799 84.19 90.9799 83.36C90.9799 82.54 90.2799 81.86 89.4199 81.84C88.5599 81.83 87.8599 82.48 87.8599 83.31Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 559,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M87.6201 77.8196C87.8201 78.0196 88.9701 77.0996 90.6501 77.0396C92.3301 76.9596 93.5901 77.7696 93.7601 77.5596C93.8501 77.4696 93.6401 77.1096 93.0801 76.7396C92.5401 76.3696 91.6201 76.0296 90.5801 76.0696C89.5301 76.1196 88.6701 76.5296 88.1801 76.9496C87.6801 77.3696 87.5201 77.7396 87.6201 77.8296V77.8196Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 563,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M82.57 90.5693C82.57 90.4693 81.52 90.3293 79.83 90.1793C79.4 90.1493 78.99 90.0793 78.91 89.7893C78.8 89.4793 78.96 89.0193 79.15 88.5093C79.51 87.4593 79.89 86.3493 80.3 85.1993C81.89 80.4893 83.02 76.6193 82.83 76.5593C82.64 76.4993 81.2 80.2693 79.61 84.9793C79.23 86.1393 78.87 87.2493 78.52 88.3093C78.38 88.7993 78.12 89.3693 78.36 89.9993C78.49 90.3193 78.81 90.5393 79.09 90.5993C79.37 90.6693 79.61 90.6693 79.82 90.6693C81.52 90.6993 82.58 90.6693 82.58 90.5693H82.57Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 567,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M82.1602 110.87C82.1602 110.87 90.3602 110.7 98.2902 105.52C98.2902 105.52 94.5102 114.09 82.3002 113.41L82.1702 110.87H82.1602Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 571,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M83.8597 94.1817C84.5697 93.4417 85.2497 93.2617 86.3097 93.3417C87.0397 93.3917 87.3997 93.7617 87.8597 94.3317C88.3197 94.9117 88.3897 96.0717 88.0097 96.6517C87.5797 97.3017 86.6397 97.4917 85.8297 97.2917C85.0197 97.0917 84.4497 96.8517 83.7797 96.3517C83.5897 96.2117 83.4597 95.9017 83.3997 95.6817C83.2097 95.0017 83.7297 94.3617 83.8597 94.1917",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 575,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M87.72 73.8809C87.9 74.3309 89.57 74.0509 91.55 74.2109C93.53 74.3309 95.14 74.8509 95.39 74.4309C95.5 74.2309 95.21 73.8109 94.55 73.3909C93.9 72.9709 92.87 72.5909 91.68 72.5009C90.49 72.4209 89.42 72.6409 88.71 72.9609C88 73.2809 87.65 73.6509 87.73 73.8609L87.72 73.8809Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 579,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M71.8998 70.8486C72.2098 71.2186 73.3498 70.7786 74.7398 70.7086C76.1298 70.5886 77.3198 70.8686 77.5698 70.4586C77.6798 70.2586 77.4798 69.8886 76.9498 69.5486C76.4298 69.2086 75.5698 68.9486 74.6198 69.0086C73.6698 69.0786 72.8498 69.4486 72.3798 69.8586C71.8998 70.2686 71.7598 70.6686 71.8898 70.8486H71.8998Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 583,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M111.81 64.3798C109.44 57.3998 99.0801 47.8698 91.7301 48.5198L80.1201 55.7798C80.9101 59.7698 84.0601 63.1998 87.9701 64.3098C90.0601 64.8998 92.5601 65.0298 93.8301 66.7898C95.1301 68.5898 94.4401 71.2598 95.6501 73.1198C97.3301 75.6998 101.77 75.4898 103.04 78.2898C103.6 79.5098 103.34 80.9898 103.92 82.1998C104.69 83.8098 108.85 85.7998 110.55 85.2298C112.24 84.6598 112.2 81.7498 112.72 80.2198C113.62 77.5998 113.21 76.2598 112.87 74.8198C112.06 71.3998 112.94 67.7098 111.81 64.3798Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 587,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M105.55 86.7596C105.52 85.8496 106.48 83.6796 107.37 83.5096C109.74 83.0496 114.06 83.0796 114.32 89.1396C114.69 97.4296 106.29 96.0896 106.26 95.8496C106.24 95.6596 105.65 89.8896 105.54 86.7596H105.55Z",
                        fill: "#FFBE9D"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 591,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M108.66 92.6013C108.66 92.6013 108.81 92.6913 109.06 92.8013C109.3 92.9013 109.72 92.9613 110.14 92.7713C111 92.4113 111.68 90.9913 111.65 89.4913C111.64 88.7313 111.45 88.0213 111.15 87.4413C110.87 86.8413 110.46 86.4413 110.01 86.3813C109.57 86.3013 109.27 86.5813 109.18 86.8113C109.08 87.0513 109.14 87.2113 109.1 87.2313C109.08 87.2513 108.92 87.1013 108.97 86.7513C109 86.5813 109.09 86.3813 109.27 86.2213C109.46 86.0513 109.75 85.9513 110.06 85.9713C110.7 85.9913 111.3 86.5513 111.63 87.1913C111.99 87.8313 112.22 88.6313 112.23 89.4813C112.25 91.1513 111.46 92.7513 110.28 93.1513C109.7 93.3313 109.21 93.1713 108.95 92.9913C108.68 92.8013 108.63 92.6113 108.66 92.6013Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 595,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M119.33 75.4187C119.39 75.4187 119.61 74.4887 119.64 72.9287C119.67 71.3787 119.45 69.2087 118.69 66.9487C117.93 64.6887 116.8 62.8187 115.84 61.5987C114.88 60.3787 114.13 59.7587 114.09 59.7987C113.94 59.9087 116.71 62.6687 118.17 67.1187C119.69 71.5487 119.14 75.4187 119.32 75.4187H119.33Z",
                        fill: "#FF725E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 599,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M82.5401 53.2805C81.7701 56.6505 76.6401 60.9805 74.3801 63.6005C70.0801 68.6105 69.2701 69.3505 69.3001 84.4505C67.2801 80.5805 65.6801 73.8105 65.8401 69.4405C66.0001 65.0705 67.6201 60.7005 70.6601 57.5605C73.7001 54.4205 78.2101 52.6405 82.5401 53.2705",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 603,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M116.04 64.2807C116.04 64.2807 115.55 64.4407 114.63 64.6307C114.17 64.7307 113.61 64.8407 112.95 64.9207C112.29 65.0007 111.54 65.1307 110.71 65.1807C109.04 65.3407 107.04 65.3607 104.83 65.2307C102.62 65.0707 100.2 64.7307 97.7099 64.1407C95.2299 63.5207 92.9199 62.7007 90.8899 61.8207C88.8699 60.9107 87.1099 59.9707 85.6999 59.0607C84.9799 58.6407 84.3699 58.1807 83.8299 57.8007C83.2799 57.4307 82.8299 57.0707 82.4799 56.7707C81.7599 56.1807 81.3699 55.8407 81.3899 55.8107C81.4099 55.7807 81.8399 56.0707 82.5999 56.6207C82.9699 56.9007 83.4399 57.2307 83.9999 57.5807C84.5599 57.9407 85.1699 58.3707 85.8999 58.7707C87.3199 59.6307 89.0799 60.5407 91.0899 61.4107C93.1099 62.2507 95.3899 63.0507 97.8499 63.6607C100.31 64.2507 102.7 64.5907 104.88 64.7807C107.07 64.9307 109.05 64.9407 110.7 64.8307C111.53 64.8107 112.28 64.7107 112.93 64.6507C113.59 64.6007 114.15 64.5207 114.61 64.4407C115.53 64.3107 116.04 64.2507 116.05 64.2807H116.04Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 607,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M116.9 70.2195C116.9 70.2195 116.56 70.3395 115.94 70.4495C115.32 70.5795 114.4 70.6995 113.27 70.7895C111 70.9795 107.82 70.9195 104.4 70.2495C100.98 69.5695 98.0198 68.4095 95.9898 67.3795C94.9698 66.8695 94.1698 66.4095 93.6398 66.0495C93.1098 65.6995 92.8198 65.4995 92.8398 65.4695C92.8898 65.3995 94.0898 66.1195 96.1398 67.0595C98.1898 67.9995 101.12 69.0895 104.5 69.7595C107.87 70.4195 111 70.5295 113.25 70.4295C115.5 70.3395 116.89 70.1195 116.91 70.2095L116.9 70.2195Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 611,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M118.99 70.4297C118.99 70.4297 119.14 70.8597 119.27 71.6797C119.41 72.4897 119.53 73.6797 119.61 75.1497C119.69 76.6197 119.71 78.3697 119.61 80.3197C119.49 82.2597 119.31 84.4097 118.76 86.6097C117.6 91.0497 114.77 94.3497 112.32 95.9897C111.1 96.8297 110 97.3297 109.24 97.6497C108.48 97.9597 108.04 98.0897 108.03 98.0597C108.01 98.0197 108.43 97.8397 109.16 97.4697C109.89 97.1097 110.94 96.5597 112.11 95.6997C113.29 94.8497 114.54 93.6397 115.64 92.0797C116.7 90.4897 117.78 88.6497 118.27 86.4897C118.81 84.3497 119.01 82.2197 119.15 80.2997C119.28 78.3697 119.29 76.6297 119.26 75.1697C119.23 73.7097 119.16 72.5297 119.08 71.7097C119.01 70.8997 118.96 70.4497 119 70.4397L118.99 70.4297Z",
                        fill: "#455A64"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 615,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M85.9299 97.2924C85.9399 97.2124 86.5199 97.4124 87.2199 96.9724C87.5599 96.7624 87.8799 96.3824 87.9599 95.8724C88.0499 95.3724 87.9099 94.7624 87.5199 94.3124C87.1299 93.8624 86.5499 93.6424 86.0199 93.6024C85.4899 93.5624 85.0099 93.7124 84.6399 93.9024C83.8999 94.3024 83.5899 94.7724 83.5199 94.7224C83.4699 94.7024 83.6699 94.1224 84.4499 93.5824C84.8399 93.3224 85.3899 93.1024 86.0399 93.1224C86.6699 93.1424 87.3899 93.3924 87.8999 93.9724C88.3999 94.5524 88.5599 95.3224 88.4199 95.9624C88.2899 96.6224 87.8199 97.0924 87.3899 97.2924C86.4799 97.7224 85.8699 97.3224 85.9199 97.2924H85.9299Z",
                        fill: "#263238"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 619,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M137.07 165.562L141.53 156.052C142.27 153.082 145.29 152.082 148.99 151.822L152.9 150.222C152.9 150.222 155.75 146.112 158.39 146.172C158.81 146.172 159.19 146.462 159.31 146.872C159.39 147.152 159.35 147.442 159.19 147.692L155.98 152.632L153.43 153.902C153.43 153.902 155.82 154.482 156.77 153.582C157.72 152.682 160.94 150.152 160.94 150.152C160.94 150.152 163.38 145.742 166.57 146.072C166.98 146.112 167.22 146.562 167.03 146.922L164.59 151.632C164.59 151.632 169.04 144.942 173.28 147.442L168.58 152.552C168.58 152.552 175.4 148.682 178.12 151.922L160.85 159.052L150.9 161.002L147.42 170.432L144.36 168.692L137.06 165.572L137.07 165.562Z",
                        fill: "#FFBE9D"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 623,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M147.41 177.923C147.41 177.923 147.94 194.822 144.82 199.482C141.7 204.142 135.91 210.452 128.73 205.402L124.24 188.393C124.24 188.393 131.55 180.372 132.88 177.092C136.2 168.902 141.54 156.062 141.54 156.062L151.07 160.303L147.43 177.923H147.41Z",
                        fill: "#FFBE9D"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 627,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M133.95 175.551C134.01 175.591 133.57 176.391 132.87 177.681C132.17 178.971 131.23 180.781 130.3 182.811C129.37 184.851 128.62 186.741 128.1 188.111C127.58 189.491 127.26 190.341 127.19 190.321C127.13 190.301 127.32 189.401 127.75 187.991C128.19 186.581 128.89 184.651 129.83 182.591C130.77 180.531 131.77 178.741 132.56 177.491C133.35 176.231 133.9 175.501 133.96 175.541L133.95 175.551Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 631,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M156 152.61C156.06 152.74 155.02 153.36 153.67 153.98C152.32 154.61 151.18 155.01 151.12 154.88C151.06 154.75 152.1 154.13 153.45 153.51C154.79 152.88 155.93 152.48 156 152.61Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 635,
                        columnNumber: 5
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M157.43 157.081C157.4 157.021 157.87 156.711 158.69 156.371C159.17 156.171 159.65 155.981 160.13 155.781C160.41 155.661 160.68 155.551 160.96 155.431C161.28 155.301 161.43 155.181 161.66 154.921C162.53 153.941 163.24 152.931 163.81 152.251C164.36 151.571 164.78 151.161 164.86 151.251C164.86 151.291 164.81 151.361 164.67 151.591C164.48 151.891 164.29 152.181 164.11 152.471C163.61 153.211 162.96 154.231 162.04 155.281C161.92 155.411 161.8 155.531 161.64 155.661C161.45 155.801 161.32 155.831 161.16 155.911C160.86 156.041 160.57 156.151 160.29 156.241C159.73 156.441 159.23 156.591 158.81 156.731C157.97 156.991 157.45 157.161 157.42 157.101L157.43 157.081Z",
                        fill: "#EB996E"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 639,
                        columnNumber: 5
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                lineNumber: 11,
                columnNumber: 4
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_17873_14827",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "439.61",
                        height: "443.8",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                        lineNumber: 646,
                        columnNumber: 6
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                    lineNumber: 645,
                    columnNumber: 5
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
                lineNumber: 644,
                columnNumber: 4
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/NoInternetIcon.tsx",
        lineNumber: 4,
        columnNumber: 3
    }, this);
}
}),
"[project]/src/app/[locale]/no-internet/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>NoInternetPage
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/translationUtils.ts [app-ssr] (ecmascript)");
// import noInternetImage from "../../../../public/assets/images/No_Internet.png";
// import Image from "next/image";
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/commonConstants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$NoInternetIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/NoInternetIcon.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
function NoInternetPage() {
    const [isOnline, setIsOnline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTranslate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleOnline = ()=>{
            setIsOnline(true);
            router.refresh();
        };
        const handleOffline = ()=>{
            setIsOnline(false);
        };
        window.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].ONLINE, handleOnline);
        window.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].OFFLINE, handleOffline);
        return ()=>{
            window.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].ONLINE, handleOnline);
            window.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$commonConstants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NETWORK_STATUS"].OFFLINE, handleOffline);
        };
    }, [
        router
    ]);
    console.log("isOnline========>", isOnline);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "no-internet-page",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "no-internet-container",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "no-internet-content",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-elements",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "floating-circle circle-1"
                            }, void 0, false, {
                                fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                lineNumber: 43,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "floating-circle circle-2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                lineNumber: 44,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "floating-circle circle-3"
                            }, void 0, false, {
                                fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                lineNumber: 45,
                                columnNumber: 7
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                        lineNumber: 42,
                        columnNumber: 6
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "main-content",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "image-container",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$NoInternetIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NoInternetIcon"], {}, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                        lineNumber: 51,
                                        columnNumber: 8
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "pulse-ring"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                        lineNumber: 52,
                                        columnNumber: 8
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                lineNumber: 50,
                                columnNumber: 7
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-content",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "title",
                                        children: t("no_internet_title")
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                        lineNumber: 56,
                                        columnNumber: 8
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "description",
                                        children: t("no_internet_desc")
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                        lineNumber: 57,
                                        columnNumber: 8
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                                lineNumber: 55,
                                columnNumber: 7
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                        lineNumber: 49,
                        columnNumber: 6
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
                lineNumber: 40,
                columnNumber: 5
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
            lineNumber: 39,
            columnNumber: 4
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/[locale]/no-internet/page.tsx",
        lineNumber: 38,
        columnNumber: 3
    }, this);
}
}),
"[project]/src/constants/routes.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "BEFORE_LOGIN_ROUTES",
    ()=>BEFORE_LOGIN_ROUTES,
    "COMMON_ROUTES",
    ()=>COMMON_ROUTES,
    "default",
    ()=>__TURBOPACK__default__export__
]);
const ROUTES = {
    HOME: "/",
    LOGIN: "/login",
    SIGNUP: "/signup",
    FORGOT_PASSWORD: "/forgot-password",
    VERIFY: "/verification",
    RESET_PASSWORD: "/reset-password",
    NO_INTERNET: "/no-internet",
    DASHBOARD: "/dashboard"
};
const BEFORE_LOGIN_ROUTES = [
    ROUTES.LOGIN,
    ROUTES.SIGNUP,
    ROUTES.FORGOT_PASSWORD,
    ROUTES.VERIFY,
    ROUTES.RESET_PASSWORD
];
const COMMON_ROUTES = [
    ROUTES.NO_INTERNET
];
const __TURBOPACK__default__export__ = ROUTES;
}),
"[project]/src/components/providers/InternetWrapper.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>InternetWrapper
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useInternetConnection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useInternetConnection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$no$2d$internet$2f$page$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/[locale]/no-internet/page.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
function InternetWrapper({ children }) {
    const { isOnline } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useInternetConnection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInternetConnection"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // If user is online but trying to access no-internet page, redirect them away
        if (isOnline && pathname.includes(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].NO_INTERNET)) {
            console.log("User has internet but is on no-internet page, redirecting to dashboard");
            try {
                router.replace(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD);
            } catch (error) {
                // Fallback to home page
                router.replace(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].DASHBOARD);
            }
        }
    }, [
        isOnline,
        pathname,
        router
    ]);
    // Show no-internet page only when actually offline
    if (!isOnline) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$no$2d$internet$2f$page$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/components/providers/InternetWrapper.tsx",
            lineNumber: 31,
            columnNumber: 10
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}),
"[project]/src/assets/images/user-placeholder.png (static in ecmascript)", ((__turbopack_context__) => {

__turbopack_context__.v("/_next/static/media/user-placeholder.370729ae.png");}),
"[project]/src/assets/images/user-placeholder.png.mjs { IMAGE => \"[project]/src/assets/images/user-placeholder.png (static in ecmascript)\" } [app-ssr] (structured image object with data url, ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$user$2d$placeholder$2e$png__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/src/assets/images/user-placeholder.png (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$user$2d$placeholder$2e$png__$28$static__in__ecmascript$29$__["default"],
    width: 250,
    height: 250,
    blurWidth: 8,
    blurHeight: 8,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAA+ElEQVR42j2Ov0sCYRjHX+/+iGio6I+oLBruHRKj6Rytpu7OIDAqoowgEqdobWgNgqgtPa8GXwRxUsRNOEER9fT8ud361ecEhw88fD+f4WGyLEsK5zydtUSt3vCIX9PK0UaO0VGuVG0rX8R5Ignj7gnfmX+U5hs5lsn+iW5/iOvkCw6ims9t6hXOYARyzHGHXqvbRyzxvAwuHlNo91yQ8wN3NMHbxxfCJwbCpzG8f/7AHU8XQdq0RLPtwBQFqFocEf0S9E+z44Ac29oJKmdX9zbJ0LHuo+pxGDcP9nZwV2GBgCStrG3wvUNVhKKaR+wfRcTq+iafK2kGRfeXhaGE8/IAAAAASUVORK5CYII="
};
}),
"[project]/src/components/svgElements/HamburgerIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const HamburgerIcon = ({ isOpen = false, className = "" })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        className: `hamburger-icon ${className}`,
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg",
        children: isOpen ? // Close icon (X)
        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M18 6L6 18",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/HamburgerIcon.tsx",
                    lineNumber: 24,
                    columnNumber: 6
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M6 6L18 18",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/HamburgerIcon.tsx",
                    lineNumber: 31,
                    columnNumber: 6
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true) : // Hamburger icon (three lines)
        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M3 12H21",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/HamburgerIcon.tsx",
                    lineNumber: 42,
                    columnNumber: 6
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M3 6H21",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/HamburgerIcon.tsx",
                    lineNumber: 49,
                    columnNumber: 6
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M3 18H21",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/HamburgerIcon.tsx",
                    lineNumber: 56,
                    columnNumber: 6
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true)
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/HamburgerIcon.tsx",
        lineNumber: 13,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = HamburgerIcon;
}),
"[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
;
;
const Button = ({ className, type, disabled = false, onClick, children, loading, title })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
            type: type,
            className: `theme-button ${className}`,
            onClick: (e)=>{
                if (onClick) {
                    onClick(e);
                }
            },
            disabled: disabled,
            title: title ?? undefined,
            children: [
                children,
                loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "spinner-border"
                }, void 0, false, {
                    fileName: "[project]/src/components/formElements/Button.tsx",
                    lineNumber: 33,
                    columnNumber: 15
                }, ("TURBOPACK compile-time value", void 0)) : null
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/formElements/Button.tsx",
            lineNumber: 21,
            columnNumber: 3
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false);
/* eslint-disable react/display-name */ Button.Icon = function({ src, align, loading }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            typeof src === "string" ? align === "left" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                src: src,
                alt: "icon",
                className: "icon-btn mr-10"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Button.tsx",
                lineNumber: 52,
                columnNumber: 6
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                src: src,
                alt: "icon",
                className: "icon-btn ml-10"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Button.tsx",
                lineNumber: 54,
                columnNumber: 6
            }, this) : src,
            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "spinner-border"
            }, void 0, false, {
                fileName: "[project]/src/components/formElements/Button.tsx",
                lineNumber: 59,
                columnNumber: 15
            }, this) : null
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Button;
}),
"[project]/src/components/svgElements/DeleteIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const DeleteIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "17",
        height: "17",
        viewBox: "0 0 17 17",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M7.16699 7.83301V11.833",
                stroke: "#E7000B",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/DeleteIcon.tsx",
                lineNumber: 10,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M9.83301 7.83301V11.833",
                stroke: "#E7000B",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/DeleteIcon.tsx",
                lineNumber: 17,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13.1663 4.5V13.8333C13.1663 14.187 13.0259 14.5261 12.7758 14.7761C12.5258 15.0262 12.1866 15.1667 11.833 15.1667H5.16634C4.81272 15.1667 4.47358 15.0262 4.22353 14.7761C3.97348 14.5261 3.83301 14.187 3.83301 13.8333V4.5",
                stroke: "#E7000B",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/DeleteIcon.tsx",
                lineNumber: 24,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M2.5 4.5H14.5",
                stroke: "#E7000B",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/DeleteIcon.tsx",
                lineNumber: 31,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M5.83301 4.49967V3.16634C5.83301 2.81272 5.97348 2.47358 6.22353 2.22353C6.47358 1.97348 6.81272 1.83301 7.16634 1.83301H9.83301C10.1866 1.83301 10.5258 1.97348 10.7758 2.22353C11.0259 2.47358 11.1663 2.81272 11.1663 3.16634V4.49967",
                stroke: "#E7000B",
                "stroke-width": "1.33333",
                "stroke-linecap": "round",
                "stroke-linejoin": "round"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/DeleteIcon.tsx",
                lineNumber: 38,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/DeleteIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = DeleteIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/CrossIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const CrossIcon = ({ className })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 20 20",
        fill: "none",
        className: className,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M19.756 0.243993C19.5997 0.0877644 19.3878 0 19.1668 0C18.9459 0 18.7339 0.0877644 18.5777 0.243993L10 8.82165L1.42235 0.243993C1.26607 0.0877644 1.05414 0 0.83317 0C0.612196 0 0.400269 0.0877644 0.243993 0.243993C0.0877644 0.400269 0 0.612196 0 0.83317C0 1.05414 0.0877644 1.26607 0.243993 1.42235L8.82165 10L0.243993 18.5777C0.0877644 18.7339 0 18.9459 0 19.1668C0 19.3878 0.0877644 19.5997 0.243993 19.756C0.400269 19.9122 0.612196 20 0.83317 20C1.05414 20 1.26607 19.9122 1.42235 19.756L10 11.1784L18.5777 19.756C18.7339 19.9122 18.9459 20 19.1668 20C19.3878 20 19.5997 19.9122 19.756 19.756C19.9122 19.5997 20 19.3878 20 19.1668C20 18.9459 19.9122 18.7339 19.756 18.5777L11.1784 10L19.756 1.42235C19.9122 1.26607 20 1.05414 20 0.83317C20 0.612196 19.9122 0.400269 19.756 0.243993Z",
            fill: "#0F1505"
        }, void 0, false, {
            fileName: "[project]/src/components/svgElements/sidebarIcon/CrossIcon.tsx",
            lineNumber: 11,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/CrossIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = CrossIcon;
}),
"[project]/src/components/svgElements/NotificationIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const NotificationIcon = ({ className, isNotification })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: ` ${isNotification ? "notification-icon" : ""}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
            xmlns: "http://www.w3.org/2000/svg",
            id: "Outline",
            viewBox: "0 0 24 24",
            width: "24",
            height: "24",
            className: className,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M22.555,13.662l-1.9-6.836A9.321,9.321,0,0,0,2.576,7.3L1.105,13.915A5,5,0,0,0,5.986,20H7.1a5,5,0,0,0,9.8,0h.838a5,5,0,0,0,4.818-6.338ZM12,22a3,3,0,0,1-2.816-2h5.632A3,3,0,0,1,12,22Zm8.126-5.185A2.977,2.977,0,0,1,17.737,18H5.986a3,3,0,0,1-2.928-3.651l1.47-6.616a7.321,7.321,0,0,1,14.2-.372l1.9,6.836A2.977,2.977,0,0,1,20.126,16.815Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/NotificationIcon.tsx",
                lineNumber: 18,
                columnNumber: 5
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/svgElements/NotificationIcon.tsx",
            lineNumber: 10,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/NotificationIcon.tsx",
        lineNumber: 9,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = NotificationIcon;
}),
"[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$user$2d$placeholder$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$src$2f$assets$2f$images$2f$user$2d$placeholder$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object__with__data__url$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/src/assets/images/user-placeholder.png.mjs { IMAGE => "[project]/src/assets/images/user-placeholder.png (static in ecmascript)" } [app-ssr] (structured image object with data url, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$HamburgerIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/HamburgerIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$DeleteIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/DeleteIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$CrossIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/CrossIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$NotificationIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/NotificationIcon.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
const Header = ({ onMenuToggle, isMenuOpen = false })=>{
    const [isNotificationOpen, setIsNotificationOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const notificationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Dummy notification data
    const notifications = [
        {
            id: 1,
            title: "New Customer Inquiries",
            category: "Customer",
            message: "5 new customer inquiries require response",
            time: "15 minutes ago"
        },
        {
            id: 2,
            title: "Staff Availability Update",
            category: "Staff",
            message: "Sarah Martinez requested schedule change",
            time: "1 hour ago"
        },
        {
            id: 3,
            title: "Payment Processed",
            category: "Payment",
            message: "Received $2,450 from Johnson Residence",
            time: "2 hours ago"
        },
        {
            id: 4,
            title: "AI Training Suggestion",
            category: "System",
            message: "New customer interaction patterns detected",
            time: "4 hours ago"
        },
        {
            id: 5,
            title: "System Backup Completed",
            category: "System",
            message: "Daily backup completed successfully",
            time: "8 hours ago"
        }
    ];
    const handleDeleteNotification = (id)=>{
        console.log("Delete notification:", id);
    // Add your delete logic here
    };
    // Close dropdown when clicking outside
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (notificationRef.current && !notificationRef.current.contains(event.target)) {
                setIsNotificationOpen(false);
            }
        };
        if (isNotificationOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        }
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [
        isNotificationOpen
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "header_main",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "header_main_inner",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    className: "hamburger-btn",
                    onClick: onMenuToggle,
                    "aria-label": "Toggle menu",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$HamburgerIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        isOpen: isMenuOpen
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 95,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 90,
                    columnNumber: 5
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "page_name",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: "Dashboard"
                    }, void 0, false, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 98,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 97,
                    columnNumber: 5
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "user_info_header",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "user_info",
                        style: {
                            cursor: "pointer"
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "notification-wrapper",
                                ref: notificationRef,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "clear-btn p-0 notification-btn",
                                        onClick: ()=>setIsNotificationOpen(!isNotificationOpen),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "notification-box",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$NotificationIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                    lineNumber: 108,
                                                    columnNumber: 10
                                                }, ("TURBOPACK compile-time value", void 0))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 107,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            notifications.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "notification-badge",
                                                children: Math.min(notifications.length, 9)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 111,
                                                columnNumber: 10
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 103,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    isNotificationOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "notification-dropdown",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "notification-header",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "notification-title-wrapper",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            children: "Notifications"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/header/Header.tsx",
                                                            lineNumber: 122,
                                                            columnNumber: 12
                                                        }, ("TURBOPACK compile-time value", void 0))
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 121,
                                                        columnNumber: 11
                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "d-flex gap-5 align-item-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "m-0 color-danger",
                                                                children: "Clear All"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 125,
                                                                columnNumber: 12
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                className: "clear-btn p-0 close-notification",
                                                                onClick: ()=>setIsNotificationOpen(false),
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$CrossIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 130,
                                                                    columnNumber: 13
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 126,
                                                                columnNumber: 12
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 124,
                                                        columnNumber: 11
                                                    }, ("TURBOPACK compile-time value", void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 120,
                                                columnNumber: 10
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "notification-list",
                                                children: notifications.map((notification)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "notification-item ",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "notification-content",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "notification-title-row",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                                children: notification.title
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                                lineNumber: 140,
                                                                                columnNumber: 15
                                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "notification-category",
                                                                                children: notification.category
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                                lineNumber: 141,
                                                                                columnNumber: 15
                                                                            }, ("TURBOPACK compile-time value", void 0))
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 139,
                                                                        columnNumber: 14
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        children: notification.message
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 145,
                                                                        columnNumber: 14
                                                                    }, ("TURBOPACK compile-time value", void 0)),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "notification-time",
                                                                        children: notification.time
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                                        lineNumber: 146,
                                                                        columnNumber: 14
                                                                    }, ("TURBOPACK compile-time value", void 0))
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 138,
                                                                columnNumber: 13
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                className: "icon-outline-btn p-0 delete-notification bg-white",
                                                                onClick: ()=>handleDeleteNotification(notification.id),
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$DeleteIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                                    fileName: "[project]/src/components/header/Header.tsx",
                                                                    lineNumber: 157,
                                                                    columnNumber: 14
                                                                }, ("TURBOPACK compile-time value", void 0))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/header/Header.tsx",
                                                                lineNumber: 151,
                                                                columnNumber: 13
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, notification.id, true, {
                                                        fileName: "[project]/src/components/header/Header.tsx",
                                                        lineNumber: 136,
                                                        columnNumber: 12
                                                    }, ("TURBOPACK compile-time value", void 0)))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/header/Header.tsx",
                                                lineNumber: 134,
                                                columnNumber: 10
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 119,
                                        columnNumber: 9
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 102,
                                columnNumber: 7
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                src: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$user$2d$placeholder$2e$png$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$src$2f$assets$2f$images$2f$user$2d$placeholder$2e$png__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object__with__data__url$2c$__ecmascript$29$__["default"],
                                alt: "avatar",
                                className: "user_img"
                            }, void 0, false, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 165,
                                columnNumber: 7
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "user_info_inner",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        children: "John Doe"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 167,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "<EMAIL>"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/header/Header.tsx",
                                        lineNumber: 168,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/header/Header.tsx",
                                lineNumber: 166,
                                columnNumber: 7
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/header/Header.tsx",
                        lineNumber: 101,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/header/Header.tsx",
                    lineNumber: 100,
                    columnNumber: 5
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/header/Header.tsx",
            lineNumber: 89,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/header/Header.tsx",
        lineNumber: 88,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = Header;
}),
"[project]/src/assets/images/logo.svg (static in ecmascript)", ((__turbopack_context__) => {

__turbopack_context__.v("/_next/static/media/logo.588859de.svg");}),
"[project]/src/assets/images/logo.svg.mjs { IMAGE => \"[project]/src/assets/images/logo.svg (static in ecmascript)\" } [app-ssr] (structured image object with data url, ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/src/assets/images/logo.svg (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$29$__["default"],
    width: 135,
    height: 36,
    blurWidth: 0,
    blurHeight: 0
};
}),
"[project]/src/components/svgElements/sidebarIcon/DashboardIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const DashboardIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "20",
        height: "20",
        viewBox: "0 0 24 24",
        fill: "none",
        style: {
            padding: "2px"
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M13.7647 6.48212V1.28294C13.7647 0.91447 13.8874 0.608587 14.1328 0.365293C14.3782 0.121764 14.6822 0 15.0448 0H22.7255C23.0884 0 23.3914 0.121764 23.6347 0.365293C23.8782 0.608587 24 0.91447 24 1.28294V6.48212C24 6.85035 23.8773 7.15612 23.6319 7.39941C23.3865 7.64294 23.0825 7.76471 22.7199 7.76471H15.0392C14.6764 7.76471 14.3733 7.64294 14.13 7.39941C13.8865 7.15612 13.7647 6.85035 13.7647 6.48212ZM0 11.4353V1.27024C0 0.910235 0.122706 0.608587 0.368117 0.365293C0.613529 0.121764 0.91753 0 1.28012 0H8.96082C9.32365 0 9.62671 0.121764 9.87 0.365293C10.1135 0.608823 10.2353 0.910588 10.2353 1.27059V11.4356C10.2353 11.7956 10.1126 12.0973 9.86718 12.3406C9.62177 12.5841 9.31777 12.7059 8.95518 12.7059H1.27447C0.911647 12.7059 0.608588 12.5841 0.365294 12.3406C0.121765 12.0971 0 11.7953 0 11.4353ZM13.7647 22.7294V12.5644C13.7647 12.2044 13.8874 11.9027 14.1328 11.6594C14.3782 11.4159 14.6822 11.2941 15.0448 11.2941H22.7255C23.0884 11.2941 23.3914 11.4159 23.6347 11.6594C23.8782 11.9029 24 12.2047 24 12.5647V22.7298C24 23.0898 23.8773 23.3914 23.6319 23.6347C23.3865 23.8782 23.0825 24 22.7199 24H15.0392C14.6764 24 14.3733 23.8782 14.13 23.6347C13.8865 23.3912 13.7647 23.0894 13.7647 22.7294ZM0 22.7171V17.5179C0 17.1496 0.122706 16.8439 0.368117 16.6006C0.613529 16.3571 0.91753 16.2353 1.28012 16.2353H8.96082C9.32365 16.2353 9.62671 16.3571 9.87 16.6006C10.1135 16.8439 10.2353 17.1496 10.2353 17.5179V22.7171C10.2353 23.0855 10.1126 23.3914 9.86718 23.6347C9.62177 23.8782 9.31777 24 8.95518 24H1.27447C0.911647 24 0.608588 23.8782 0.365294 23.6347C0.121765 23.3914 0 23.0855 0 22.7171ZM2.11765 10.5882H8.11765V2.11765H2.11765V10.5882ZM15.8824 21.8824H21.8824V13.4118H15.8824V21.8824ZM15.8824 5.64706H21.8824V2.11765H15.8824V5.64706ZM2.11765 21.8824H8.11765V18.3529H2.11765V21.8824Z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgElements/sidebarIcon/DashboardIcon.tsx",
            lineNumber: 11,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/DashboardIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = DashboardIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/ServiceIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const ServiceIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M23.613 6.83514L22.2609 4.00867C21.9119 3.27855 21.2709 2.71245 20.5028 2.45641L14.2055 0.35706C12.7774 -0.11902 11.2053 -0.11902 9.77725 0.35706L3.4809 2.45641C2.71186 2.71245 2.07082 3.27955 1.7218 4.01067L0.435731 6.71812C-0.0412954 7.51025 -0.1313 8.46041 0.188717 9.32756C0.497735 10.1677 1.15277 10.8178 1.99082 11.1239L1.98582 17.4389C1.98382 19.5973 3.35789 21.5056 5.40401 22.1877L9.76925 23.6429C10.4833 23.881 11.2333 24 11.9834 24C12.7334 24 13.4835 23.881 14.1975 23.6429L18.5667 22.1857C20.6099 21.5046 21.9839 19.6003 21.9859 17.4459L21.9909 11.1249C22.837 10.8278 23.499 10.1787 23.81 9.33456C24.1271 8.47641 24.039 7.53526 23.613 6.83514ZM10.4103 2.25538C11.4303 1.91532 12.5534 1.91532 13.5725 2.25538L17.8207 3.67161L11.9984 5.62194L6.16405 3.67161L10.4103 2.25538ZM2.19483 7.66528L3.5219 4.88281C3.56991 4.90782 10.5793 7.25621 10.5793 7.25621L8.95821 10.5148C8.71919 10.9128 8.22316 11.0969 7.78514 10.9488L2.70386 9.25554C2.40684 9.15653 2.17383 8.93049 2.06482 8.63644C1.95582 8.34239 1.98682 8.02034 2.19383 7.66628L2.19483 7.66528ZM6.03604 20.2904C4.80797 19.8813 3.98393 18.7361 3.98493 17.4419L3.98993 11.793L7.1521 12.8471C8.49618 13.2952 9.94426 12.7601 10.7113 11.4759L10.9893 10.9188L10.9843 21.9017C10.7873 21.8616 10.5933 21.8106 10.4033 21.7466L6.03704 20.2914L6.03604 20.2904ZM17.9337 20.2894L13.5635 21.7466C13.3735 21.8096 13.1794 21.8616 12.9824 21.9017L12.9874 10.9048L13.3064 11.5449C13.8625 12.4731 14.8385 13.0042 15.8676 13.0042C16.1866 13.0042 16.5106 12.9532 16.8276 12.8471L19.9888 11.794L19.9838 17.4459C19.9838 18.7381 19.1578 19.8813 17.9317 20.2904L17.9337 20.2894ZM21.9339 8.64344C21.8289 8.92849 21.6029 9.14753 21.3149 9.24354L16.1966 10.9498C15.7586 11.0969 15.2626 10.9148 15.0605 10.5848L13.4055 7.26121C13.4055 7.26121 20.4118 4.90982 20.4598 4.88381L21.8529 7.7853C22.0099 8.04634 22.0389 8.35939 21.9339 8.64444V8.64344Z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgElements/sidebarIcon/ServiceIcon.tsx",
            lineNumber: 10,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/ServiceIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = ServiceIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/BookingsIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const BookingsIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M19 2H18V1C18 0.448 17.553 0 17 0C16.447 0 16 0.448 16 1V2H8V1C8 0.448 7.553 0 7 0C6.447 0 6 0.448 6 1V2H5C2.243 2 0 4.243 0 7V19C0 21.757 2.243 24 5 24H19C21.757 24 24 21.757 24 19V7C24 4.243 21.757 2 19 2ZM5 4H19C20.654 4 22 5.346 22 7V8H2V7C2 5.346 3.346 4 5 4ZM19 22H5C3.346 22 2 20.654 2 19V10H22V19C22 20.654 20.654 22 19 22ZM17.832 13.152C18.216 13.549 18.204 14.183 17.807 14.566L13.067 19.134C12.514 19.687 11.76 20 10.959 20C10.158 20 9.404 19.688 8.838 19.121L6.586 17.029C6.182 16.653 6.158 16.02 6.534 15.616C6.912 15.211 7.544 15.189 7.947 15.564L10.225 17.681C10.658 18.111 11.288 18.083 11.664 17.707L16.418 13.125C16.816 12.742 17.448 12.754 17.832 13.151V13.152Z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgElements/sidebarIcon/BookingsIcon.tsx",
            lineNumber: 10,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/BookingsIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = BookingsIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const CustomersIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    d: "M7.5 13C6.60998 13 5.73995 12.7361 4.99993 12.2416C4.25991 11.7471 3.68314 11.0443 3.34254 10.2221C3.00195 9.39981 2.91283 8.49501 3.08647 7.6221C3.2601 6.74918 3.68868 5.94736 4.31802 5.31802C4.94736 4.68868 5.74918 4.2601 6.62209 4.08647C7.49501 3.91283 8.39981 4.00195 9.22208 4.34254C10.0443 4.68314 10.7471 5.25991 11.2416 5.99994C11.7361 6.73996 12 7.60999 12 8.5C11.9987 9.69307 11.5241 10.8369 10.6805 11.6805C9.83689 12.5241 8.69307 12.9987 7.5 13ZM7.5 6C7.00555 6 6.5222 6.14662 6.11107 6.42133C5.69995 6.69603 5.37952 7.08648 5.1903 7.54329C5.00108 8.00011 4.95157 8.50278 5.04804 8.98773C5.1445 9.47268 5.3826 9.91814 5.73223 10.2678C6.08186 10.6174 6.52732 10.8555 7.01227 10.952C7.49723 11.0484 7.99989 10.9989 8.45671 10.8097C8.91352 10.6205 9.30397 10.3001 9.57867 9.88893C9.85338 9.4778 10 8.99446 10 8.5C10 7.83696 9.73661 7.20108 9.26777 6.73223C8.79893 6.26339 8.16304 6 7.5 6ZM15 23V22.5C15 20.5109 14.2098 18.6032 12.8033 17.1967C11.3968 15.7902 9.48912 15 7.5 15C5.51088 15 3.60322 15.7902 2.1967 17.1967C0.790176 18.6032 0 20.5109 0 22.5L0 23C0 23.2652 0.105357 23.5196 0.292893 23.7071C0.48043 23.8946 0.734784 24 1 24C1.26522 24 1.51957 23.8946 1.70711 23.7071C1.89464 23.5196 2 23.2652 2 23V22.5C2 21.0413 2.57946 19.6424 3.61091 18.6109C4.64236 17.5795 6.04131 17 7.5 17C8.95869 17 10.3576 17.5795 11.3891 18.6109C12.4205 19.6424 13 21.0413 13 22.5V23C13 23.2652 13.1054 23.5196 13.2929 23.7071C13.4804 23.8946 13.7348 24 14 24C14.2652 24 14.5196 23.8946 14.7071 23.7071C14.8946 23.5196 15 23.2652 15 23ZM24 18C24 16.6487 23.6088 15.3263 22.8737 14.1924C22.1386 13.0585 21.091 12.1616 19.8574 11.61C18.6238 11.0584 17.2569 10.8756 15.9218 11.0837C14.5866 11.2919 13.3402 11.8821 12.333 12.783C12.2338 12.8702 12.1528 12.9762 12.0948 13.0949C12.0367 13.2135 12.0028 13.3425 11.9949 13.4744C11.987 13.6063 12.0053 13.7384 12.0487 13.8631C12.0922 13.9879 12.1599 14.1028 12.2481 14.2012C12.3362 14.2996 12.4429 14.3796 12.5621 14.4366C12.6813 14.4935 12.8106 14.5262 12.9426 14.5329C13.0745 14.5396 13.2064 14.5201 13.3308 14.4754C13.4551 14.4308 13.5694 14.362 13.667 14.273C14.3865 13.6296 15.2767 13.2082 16.2304 13.0597C17.1841 12.9111 18.1604 13.0417 19.0414 13.4358C19.9225 13.8299 20.6706 14.4705 21.1956 15.2804C21.7206 16.0903 22 17.0348 22 18C22 18.2652 22.1054 18.5196 22.2929 18.7071C22.4804 18.8946 22.7348 19 23 19C23.2652 19 23.5196 18.8946 23.7071 18.7071C23.8946 18.5196 24 18.2652 24 18ZM17.5 9C16.61 9 15.74 8.73608 14.9999 8.24162C14.2599 7.74715 13.6831 7.04434 13.3425 6.22208C13.0019 5.39981 12.9128 4.49501 13.0865 3.6221C13.2601 2.74918 13.6887 1.94736 14.318 1.31802C14.9474 0.688685 15.7492 0.260102 16.6221 0.0864683C17.495 -0.0871652 18.3998 0.00194979 19.2221 0.342544C20.0443 0.683138 20.7471 1.25991 21.2416 1.99994C21.7361 2.73996 22 3.60999 22 4.5C21.9987 5.69307 21.5241 6.83689 20.6805 7.68052C19.8369 8.52415 18.6931 8.99868 17.5 9ZM17.5 2C17.0055 2 16.5222 2.14662 16.1111 2.42133C15.7 2.69603 15.3795 3.08648 15.1903 3.54329C15.0011 4.00011 14.9516 4.50278 15.048 4.98773C15.1445 5.47268 15.3826 5.91814 15.7322 6.26777C16.0819 6.6174 16.5273 6.8555 17.0123 6.95197C17.4972 7.04843 17.9999 6.99892 18.4567 6.8097C18.9135 6.62048 19.304 6.30005 19.5787 5.88893C19.8534 5.4778 20 4.99446 20 4.5C20 3.83696 19.7366 3.20108 19.2678 2.73223C18.7989 2.26339 18.163 2 17.5 2Z"
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx",
                    lineNumber: 11,
                    columnNumber: 5
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx",
                lineNumber: 10,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    id: "clip0_488_1897",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "24",
                        height: "24",
                        fill: "white"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx",
                        lineNumber: 18,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx",
                    lineNumber: 17,
                    columnNumber: 5
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx",
                lineNumber: 16,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = CustomersIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/StaffIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const StaffIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M22.9992 10.9996H20.9992V8.99954C20.9992 8.73432 20.8938 8.47996 20.7063 8.29241C20.5187 8.10487 20.2644 7.99951 19.9991 7.99951C19.7339 7.99951 19.4796 8.10487 19.292 8.29241C19.1045 8.47996 18.9991 8.73432 18.9991 8.99954V10.9996H16.9991C16.7338 10.9996 16.4795 11.105 16.2919 11.2925C16.1044 11.48 15.999 11.7344 15.999 11.9996C15.999 12.2649 16.1044 12.5192 16.2919 12.7068C16.4795 12.8943 16.7338 12.9997 16.9991 12.9997H18.9991V14.9997C18.9991 15.265 19.1045 15.5193 19.292 15.7069C19.4796 15.8944 19.7339 15.9998 19.9991 15.9998C20.2644 15.9998 20.5187 15.8944 20.7063 15.7069C20.8938 15.5193 20.9992 15.265 20.9992 14.9997V12.9997H22.9992C23.2645 12.9997 23.5188 12.8943 23.7064 12.7068C23.8939 12.5192 23.9993 12.2649 23.9993 11.9996C23.9993 11.7344 23.8939 11.48 23.7064 11.2925C23.5188 11.105 23.2645 10.9996 22.9992 10.9996Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/StaffIcon.tsx",
                lineNumber: 10,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M9.00018 12.0004C10.1869 12.0004 11.347 11.6485 12.3337 10.9892C13.3204 10.3298 14.0895 9.39275 14.5436 8.29636C14.9978 7.19997 15.1166 5.99353 14.8851 4.82961C14.6536 3.66569 14.0821 2.59656 13.243 1.75742C12.4038 0.918275 11.3347 0.346813 10.1708 0.115295C9.00684 -0.116224 7.8004 0.0025998 6.70401 0.45674C5.60762 0.910879 4.67052 1.67994 4.01121 2.66666C3.3519 3.65339 3 4.81346 3 6.00019C3.00159 7.59105 3.63426 9.11629 4.75917 10.2412C5.88408 11.3661 7.40932 11.9988 9.00018 12.0004ZM9.00018 2.00006C9.79133 2.00006 10.5647 2.23467 11.2225 2.67421C11.8804 3.11375 12.3931 3.73848 12.6958 4.46941C12.9986 5.20033 13.0778 6.00462 12.9234 6.78057C12.7691 7.55652 12.3881 8.26927 11.8287 8.8287C11.2693 9.38813 10.5565 9.7691 9.78057 9.92345C9.00462 10.0778 8.20033 9.99858 7.4694 9.69582C6.73848 9.39306 6.11374 8.88035 5.6742 8.22254C5.23466 7.56472 5.00006 6.79134 5.00006 6.00019C5.00006 4.93929 5.4215 3.92184 6.17167 3.17167C6.92184 2.4215 7.93929 2.00006 9.00018 2.00006Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/StaffIcon.tsx",
                lineNumber: 14,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M9.00028 13.9995C6.61407 14.0022 4.32635 14.9512 2.63904 16.6386C0.951738 18.3259 0.00264693 20.6136 0 22.9998C0 23.265 0.10536 23.5194 0.292902 23.7069C0.480444 23.8945 0.734806 23.9998 1.00003 23.9998C1.26526 23.9998 1.51962 23.8945 1.70716 23.7069C1.8947 23.5194 2.00006 23.265 2.00006 22.9998C2.00006 21.1432 2.73758 19.3627 4.05038 18.0499C5.36317 16.7371 7.1437 15.9996 9.00028 15.9996C10.8568 15.9996 12.6374 16.7371 13.9502 18.0499C15.263 19.3627 16.0005 21.1432 16.0005 22.9998C16.0005 23.265 16.1059 23.5194 16.2934 23.7069C16.4809 23.8945 16.7353 23.9998 17.0005 23.9998C17.2657 23.9998 17.5201 23.8945 17.7077 23.7069C17.8952 23.5194 18.0006 23.265 18.0006 22.9998C17.9979 20.6136 17.0488 18.3259 15.3615 16.6386C13.6742 14.9512 11.3865 14.0022 9.00028 13.9995Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/StaffIcon.tsx",
                lineNumber: 18,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/StaffIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = StaffIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/PaymentsIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const PaymentsIcon = ({ className })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "18",
        viewBox: "0 0 24 18",
        fill: "none",
        className: className,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M5.5 14C6.32843 14 7 13.3284 7 12.5C7 11.6716 6.32843 11 5.5 11C4.67157 11 4 11.6716 4 12.5C4 13.3284 4.67157 14 5.5 14Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/PaymentsIcon.tsx",
                lineNumber: 11,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M19 0H5C3.67441 0.00158786 2.40356 0.528882 1.46622 1.46622C0.528882 2.40356 0.00158786 3.67441 0 5L0 13C0.00158786 14.3256 0.528882 15.5964 1.46622 16.5338C2.40356 17.4711 3.67441 17.9984 5 18H19C20.3256 17.9984 21.5964 17.4711 22.5338 16.5338C23.4711 15.5964 23.9984 14.3256 24 13V5C23.9984 3.67441 23.4711 2.40356 22.5338 1.46622C21.5964 0.528882 20.3256 0.00158786 19 0ZM5 2H19C19.7956 2 20.5587 2.31607 21.1213 2.87868C21.6839 3.44129 22 4.20435 22 5H2C2 4.20435 2.31607 3.44129 2.87868 2.87868C3.44129 2.31607 4.20435 2 5 2ZM19 16H5C4.20435 16 3.44129 15.6839 2.87868 15.1213C2.31607 14.5587 2 13.7956 2 13V7H22V13C22 13.7956 21.6839 14.5587 21.1213 15.1213C20.5587 15.6839 19.7956 16 19 16Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/PaymentsIcon.tsx",
                lineNumber: 15,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/PaymentsIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = PaymentsIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const SettingsIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        style: {
            stroke: "none"
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("g", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M12 7.99707C11.2089 7.99707 10.4355 8.23167 9.77772 8.67119C9.11993 9.11072 8.60723 9.73543 8.30448 10.4663C8.00173 11.1972 7.92252 12.0015 8.07686 12.7774C8.2312 13.5534 8.61216 14.2661 9.17157 14.8255C9.73099 15.3849 10.4437 15.7659 11.2196 15.9202C11.9956 16.0746 12.7998 15.9953 13.5307 15.6926C14.2616 15.3898 14.8864 14.8771 15.3259 14.2194C15.7654 13.5616 16 12.7882 16 11.9971C16 10.9362 15.5786 9.91879 14.8284 9.16864C14.0783 8.4185 13.0609 7.99707 12 7.99707ZM12 13.9971C11.6044 13.9971 11.2178 13.8798 10.8889 13.66C10.56 13.4402 10.3036 13.1279 10.1522 12.7624C10.0009 12.397 9.96126 11.9949 10.0384 11.6069C10.1156 11.2189 10.3061 10.8626 10.5858 10.5829C10.8655 10.3032 11.2219 10.1127 11.6098 10.0355C11.9978 9.95833 12.3999 9.99794 12.7654 10.1493C13.1308 10.3007 13.4432 10.557 13.6629 10.8859C13.8827 11.2148 14 11.6015 14 11.9971C14 12.5275 13.7893 13.0362 13.4142 13.4113C13.0391 13.7864 12.5304 13.9971 12 13.9971Z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
                        lineNumber: 12,
                        columnNumber: 5
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        d: "M21.2938 13.8971L20.8498 13.6411C21.0497 12.5535 21.0497 11.4386 20.8498 10.3511L21.2938 10.0951C21.6352 9.89809 21.9345 9.63578 22.1746 9.32313C22.4147 9.01048 22.5908 8.65361 22.6929 8.27289C22.7951 7.89217 22.8212 7.49506 22.7699 7.10423C22.7186 6.7134 22.5908 6.33651 22.3938 5.99507C22.1968 5.65363 21.9345 5.35434 21.6219 5.11427C21.3092 4.87421 20.9523 4.69808 20.5716 4.59593C20.1909 4.49379 19.7938 4.46763 19.403 4.51896C19.0121 4.57028 18.6352 4.69809 18.2938 4.89507L17.8488 5.15207C17.0083 4.43399 16.0425 3.87732 14.9998 3.51007V2.99707C14.9998 2.20142 14.6837 1.43836 14.1211 0.87575C13.5585 0.313141 12.7954 -0.00292969 11.9998 -0.00292969C11.2041 -0.00292969 10.4411 0.313141 9.87848 0.87575C9.31587 1.43836 8.9998 2.20142 8.9998 2.99707V3.51007C7.95719 3.87864 6.99165 4.43667 6.1518 5.15607L5.7048 4.89707C5.01524 4.49925 4.19588 4.39164 3.42698 4.59793C2.65808 4.80422 2.00262 5.30751 1.6048 5.99707C1.20697 6.68663 1.09937 7.50599 1.30566 8.27489C1.51195 9.04379 2.01524 9.69925 2.7048 10.0971L3.1488 10.3531C2.94891 11.4406 2.94891 12.5555 3.1488 13.6431L2.7048 13.8991C2.01524 14.2969 1.51195 14.9524 1.30566 15.7213C1.09937 16.4902 1.20697 17.3095 1.6048 17.9991C2.00262 18.6886 2.65808 19.1919 3.42698 19.3982C4.19588 19.6045 5.01524 19.4969 5.7048 19.0991L6.1498 18.8421C6.99056 19.5603 7.95678 20.1169 8.9998 20.4841V20.9971C8.9998 21.7927 9.31587 22.5558 9.87848 23.1184C10.4411 23.681 11.2041 23.9971 11.9998 23.9971C12.7954 23.9971 13.5585 23.681 14.1211 23.1184C14.6837 22.5558 14.9998 21.7927 14.9998 20.9971V20.4841C16.0424 20.1155 17.008 19.5575 17.8478 18.8381L18.2948 19.0961C18.9844 19.4939 19.8037 19.6015 20.5726 19.3952C21.3415 19.1889 21.997 18.6856 22.3948 17.9961C22.7926 17.3065 22.9002 16.4872 22.6939 15.7183C22.4876 14.9494 21.9844 14.2939 21.2948 13.8961L21.2938 13.8971ZM18.7458 10.1211C19.0844 11.3481 19.0844 12.644 18.7458 13.8711C18.6867 14.0846 18.7002 14.3118 18.7841 14.5169C18.8681 14.722 19.0178 14.8933 19.2098 15.0041L20.2938 15.6301C20.5236 15.7627 20.6913 15.9811 20.7601 16.2374C20.8288 16.4937 20.7929 16.7668 20.6603 16.9966C20.5277 17.2264 20.3092 17.3941 20.053 17.4628C19.7967 17.5316 19.5236 17.4957 19.2938 17.3631L18.2078 16.7351C18.0157 16.6238 17.792 16.5796 17.572 16.6095C17.3521 16.6394 17.1483 16.7416 16.9928 16.9001C16.1027 17.8087 14.9814 18.4571 13.7498 18.7751C13.5348 18.8303 13.3444 18.9556 13.2084 19.131C13.0724 19.3064 12.9987 19.5221 12.9988 19.7441V20.9971C12.9988 21.2623 12.8934 21.5166 12.7059 21.7042C12.5184 21.8917 12.264 21.9971 11.9988 21.9971C11.7336 21.9971 11.4792 21.8917 11.2917 21.7042C11.1042 21.5166 10.9988 21.2623 10.9988 20.9971V19.7451C10.9989 19.5231 10.9252 19.3074 10.7892 19.132C10.6532 18.9566 10.4628 18.8313 10.2478 18.7761C9.01615 18.4568 7.89513 17.807 7.0058 16.8971C6.85032 16.7386 6.64654 16.6364 6.42655 16.6065C6.20657 16.5766 5.9829 16.6208 5.7908 16.7321L4.7068 17.3591C4.59303 17.4258 4.46719 17.4693 4.33652 17.4872C4.20586 17.505 4.07295 17.4969 3.94545 17.4632C3.81795 17.4295 3.69838 17.3709 3.59362 17.2908C3.48886 17.2106 3.40098 17.1106 3.33504 16.9964C3.26909 16.8822 3.22639 16.7561 3.2094 16.6253C3.1924 16.4945 3.20144 16.3617 3.23599 16.2344C3.27054 16.1071 3.32993 15.9879 3.41073 15.8837C3.49153 15.7795 3.59215 15.6923 3.7068 15.6271L4.7908 15.0011C4.98275 14.8903 5.13247 14.719 5.21646 14.5139C5.30044 14.3088 5.31393 14.0816 5.2548 13.8681C4.91616 12.641 4.91616 11.3451 5.2548 10.1181C5.31286 9.90495 5.29873 9.67861 5.21461 9.47436C5.13049 9.27012 4.98111 9.09948 4.7898 8.98907L3.7058 8.36307C3.47599 8.23046 3.30827 8.01199 3.23954 7.75572C3.17081 7.49946 3.20669 7.22638 3.3393 6.99657C3.47191 6.76676 3.69038 6.59904 3.94664 6.53031C4.20291 6.46158 4.47599 6.49746 4.7058 6.63007L5.7918 7.25807C5.98338 7.36958 6.20658 7.41428 6.42633 7.38514C6.64607 7.356 6.84991 7.25466 7.0058 7.09707C7.89589 6.18841 9.01722 5.54009 10.2488 5.22207C10.4644 5.16663 10.6554 5.04082 10.7914 4.86457C10.9275 4.68832 11.0008 4.47171 10.9998 4.24907V2.99707C10.9998 2.73185 11.1052 2.4775 11.2927 2.28996C11.4802 2.10243 11.7346 1.99707 11.9998 1.99707C12.265 1.99707 12.5194 2.10243 12.7069 2.28996C12.8944 2.4775 12.9998 2.73185 12.9998 2.99707V4.24907C12.9997 4.47103 13.0734 4.68671 13.2094 4.86215C13.3454 5.03759 13.5358 5.1628 13.7508 5.21807C14.9828 5.53722 16.1042 6.18695 16.9938 7.09707C17.1493 7.25554 17.3531 7.35776 17.573 7.38764C17.793 7.41751 18.0167 7.37333 18.2088 7.26207L19.2928 6.63507C19.4066 6.56837 19.5324 6.52484 19.6631 6.50697C19.7937 6.48911 19.9266 6.49726 20.0541 6.53097C20.1816 6.56468 20.3012 6.62328 20.406 6.70338C20.5107 6.78349 20.5986 6.88353 20.6646 6.99774C20.7305 7.11195 20.7732 7.23808 20.7902 7.36886C20.8072 7.49964 20.7982 7.63249 20.7636 7.75976C20.7291 7.88704 20.6697 8.00622 20.5889 8.11044C20.5081 8.21467 20.4074 8.30189 20.2928 8.36707L19.2088 8.99307C19.0178 9.10378 18.8689 9.27455 18.7851 9.47876C18.7014 9.68298 18.6876 9.90917 18.7458 10.1221V10.1211Z"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
                        lineNumber: 16,
                        columnNumber: 5
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
                lineNumber: 11,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("clipPath", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("rect", {
                        width: "24",
                        height: "24",
                        fill: "white",
                        transform: "translate(0 -0.00292969)"
                    }, void 0, false, {
                        fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
                        lineNumber: 23,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
                    lineNumber: 22,
                    columnNumber: 5
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
                lineNumber: 21,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = SettingsIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/SupportIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const SupportIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        style: {
            stroke: "none"
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12 -0.00292969C9.62663 -0.00292969 7.30655 0.700858 5.33316 2.01943C3.35977 3.33801 1.8217 5.21215 0.913451 7.40487C0.00519943 9.59758 -0.232441 12.0104 0.230582 14.3382C0.693605 16.6659 1.83649 18.8041 3.51472 20.4823C5.19295 22.1606 7.33115 23.3035 9.65892 23.7665C11.9867 24.2295 14.3995 23.9919 16.5922 23.0836C18.7849 22.1754 20.6591 20.6373 21.9776 18.6639C23.2962 16.6905 24 14.3704 24 11.9971C23.9966 8.81553 22.7312 5.76528 20.4815 3.51559C18.2318 1.2659 15.1815 0.000511393 12 -0.00292969ZM12 21.9971C10.0222 21.9971 8.08879 21.4106 6.4443 20.3118C4.79981 19.2129 3.51809 17.6512 2.76121 15.8239C2.00433 13.9966 1.8063 11.986 2.19215 10.0462C2.578 8.10636 3.53041 6.32453 4.92894 4.926C6.32746 3.52748 8.10929 2.57507 10.0491 2.18922C11.9889 1.80336 13.9996 2.0014 15.8268 2.75827C17.6541 3.51515 19.2159 4.79688 20.3147 6.44137C21.4135 8.08586 22 10.0193 22 11.9971C21.9971 14.6483 20.9426 17.1902 19.0679 19.0649C17.1931 20.9397 14.6513 21.9942 12 21.9971Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/SupportIcon.tsx",
                lineNumber: 11,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M12.717 5.0599C12.1403 4.95483 11.5476 4.97779 10.9808 5.12714C10.414 5.2765 9.88691 5.54861 9.43689 5.92421C8.98688 6.29981 8.62491 6.76972 8.37661 7.3007C8.12831 7.83168 7.99974 8.41074 8 8.9969C8 9.26212 8.10536 9.51647 8.29289 9.70401C8.48043 9.89154 8.73478 9.9969 9 9.9969C9.26522 9.9969 9.51957 9.89154 9.70711 9.70401C9.89464 9.51647 10 9.26212 10 8.9969C9.99975 8.70268 10.0644 8.41202 10.1894 8.14566C10.3144 7.8793 10.4966 7.64379 10.723 7.45593C10.9495 7.26807 11.2146 7.13249 11.4994 7.05885C11.7843 6.98521 12.0819 6.97532 12.371 7.0299C12.766 7.10659 13.1293 7.29929 13.4142 7.58339C13.6992 7.8675 13.8931 8.2301 13.971 8.6249C14.0497 9.0393 13.9954 9.46798 13.8158 9.84964C13.6361 10.2313 13.3405 10.5464 12.971 10.7499C12.3592 11.1044 11.8536 11.6164 11.5069 12.2327C11.1603 12.849 10.9852 13.547 11 14.2539V14.9969C11 15.2621 11.1054 15.5165 11.2929 15.704C11.4804 15.8915 11.7348 15.9969 12 15.9969C12.2652 15.9969 12.5196 15.8915 12.7071 15.704C12.8946 15.5165 13 15.2621 13 14.9969V14.2539C12.9875 13.9059 13.0668 13.5608 13.2301 13.2532C13.3933 12.9456 13.6347 12.6865 13.93 12.5019C14.6545 12.104 15.238 11.4914 15.6001 10.7483C15.9622 10.0053 16.0853 9.16826 15.9523 8.35243C15.8193 7.53659 15.4368 6.78199 14.8575 6.1924C14.2782 5.6028 13.5304 5.20717 12.717 5.0599Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/SupportIcon.tsx",
                lineNumber: 15,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M13 17.9971C13 17.4448 12.5523 16.9971 12 16.9971C11.4477 16.9971 11 17.4448 11 17.9971C11 18.5494 11.4477 18.9971 12 18.9971C12.5523 18.9971 13 18.5494 13 17.9971Z"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/SupportIcon.tsx",
                lineNumber: 19,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/SupportIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = SupportIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/LogoutIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const LogoutIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        style: {
            stroke: "none"
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M11.4757 15C11.2105 15 10.9561 15.1054 10.7686 15.2929C10.5811 15.4804 10.4757 15.7348 10.4757 16V19C10.4757 19.7956 10.1597 20.5587 9.59707 21.1213C9.03448 21.6839 8.27143 22 7.47581 22H4.99987C4.20424 22 3.4412 21.6839 2.87861 21.1213C2.31601 20.5587 1.99995 19.7956 1.99995 19V5C1.99995 4.20435 2.31601 3.44129 2.87861 2.87868C3.4412 2.31607 4.20424 2 4.99987 2H7.47581C8.27143 2 9.03448 2.31607 9.59707 2.87868C10.1597 3.44129 10.4757 4.20435 10.4757 5V8C10.4757 8.26522 10.5811 8.51957 10.7686 8.70711C10.9561 8.89464 11.2105 9 11.4757 9C11.7409 9 11.9953 8.89464 12.1828 8.70711C12.3703 8.51957 12.4757 8.26522 12.4757 8V5C12.4741 3.67441 11.9468 2.40356 11.0095 1.46622C10.0722 0.528882 8.80137 0.00158786 7.47581 0H4.99987C3.67431 0.00158786 2.4035 0.528882 1.46618 1.46622C0.528868 2.40356 0.00158782 3.67441 0 5L0 19C0.00158782 20.3256 0.528868 21.5964 1.46618 22.5338C2.4035 23.4711 3.67431 23.9984 4.99987 24H7.47581C8.80137 23.9984 10.0722 23.4711 11.0095 22.5338C11.9468 21.5964 12.4741 20.3256 12.4757 19V16C12.4757 15.7348 12.3703 15.4804 12.1828 15.2929C11.9953 15.1054 11.7409 15 11.4757 15Z",
                fill: "#BE0000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/LogoutIcon.tsx",
                lineNumber: 11,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                d: "M22.8675 9.87859L18.2816 5.29259C18.1894 5.19708 18.079 5.1209 17.957 5.06849C17.835 5.01608 17.7038 4.9885 17.5711 4.98734C17.4383 4.98619 17.3066 5.01149 17.1837 5.06177C17.0608 5.11205 16.9492 5.18631 16.8553 5.2802C16.7614 5.37409 16.6871 5.48574 16.6369 5.60864C16.5866 5.73154 16.5613 5.86321 16.5624 5.99599C16.5636 6.12877 16.5912 6.25999 16.6436 6.382C16.696 6.504 16.7722 6.61435 16.8677 6.70659L21.1296 10.9696L6.00095 10.9996C5.73574 10.9996 5.48139 11.1049 5.29386 11.2925C5.10633 11.48 5.00098 11.7344 5.00098 11.9996C5.00098 12.2648 5.10633 12.5192 5.29386 12.7067C5.48139 12.8942 5.73574 12.9996 6.00095 12.9996L21.1886 12.9686L16.8657 17.2926C16.7702 17.3848 16.694 17.4952 16.6416 17.6172C16.5892 17.7392 16.5616 17.8704 16.5604 18.0032C16.5593 18.136 16.5846 18.2676 16.6349 18.3905C16.6851 18.5134 16.7594 18.6251 16.8533 18.719C16.9472 18.8129 17.0588 18.8871 17.1817 18.9374C17.3046 18.9877 17.4363 19.013 17.5691 19.0118C17.7018 19.0107 17.833 18.9831 17.955 18.9307C18.077 18.8783 18.1874 18.8021 18.2796 18.7066L22.8655 14.1206C23.4282 13.5583 23.7445 12.7955 23.7449 12C23.7452 11.2045 23.4296 10.4414 22.8675 9.87859Z",
                fill: "#BE0000"
            }, void 0, false, {
                fileName: "[project]/src/components/svgElements/sidebarIcon/LogoutIcon.tsx",
                lineNumber: 15,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/LogoutIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = LogoutIcon;
}),
"[project]/src/components/svgElements/sidebarIcon/AIbotIcon.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const AIbotIcon = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        style: {
            stroke: "none"
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
            d: "M22.5 8.99707H22V7.99707C22 5.24007 19.757 2.99707 17 2.99707H13V0.99707C13 0.44507 12.553 -0.00292969 12 -0.00292969C11.447 -0.00292969 11 0.44507 11 0.99707V2.99707H7C4.243 2.99707 2 5.24007 2 7.99707V8.99707H1.5C0.673 8.99707 0 9.67007 0 10.4971V13.4971C0 14.3241 0.673 14.9971 1.5 14.9971H2V15.9971C2 18.7541 4.243 20.9971 7 20.9971H14.697L18.66 23.6391C19.02 23.8791 19.435 24.0001 19.851 24.0001C20.199 24.0001 20.547 23.9161 20.866 23.7451C21.565 23.3701 22 22.6451 22 21.8511V14.9961H22.5C23.327 14.9961 24 14.3231 24 13.4961V10.4961C24 9.66907 23.327 8.99607 22.5 8.99607V8.99707ZM20 21.8521C20 21.8741 20 21.9411 19.922 21.9821C19.842 22.0251 19.786 21.9861 19.77 21.9751L15.555 19.1651C15.391 19.0561 15.198 18.9971 15 18.9971H7C5.346 18.9971 4 17.6511 4 15.9971V7.99707C4 6.34307 5.346 4.99707 7 4.99707H17C18.654 4.99707 20 6.34307 20 7.99707V21.8521ZM7 9.49707C7 8.66907 7.672 7.99707 8.5 7.99707C9.328 7.99707 10 8.66907 10 9.49707C10 10.3251 9.328 10.9971 8.5 10.9971C7.672 10.9971 7 10.3251 7 9.49707ZM17 9.49707C17 10.3251 16.328 10.9971 15.5 10.9971C14.672 10.9971 14 10.3251 14 9.49707C14 8.66907 14.672 7.99707 15.5 7.99707C16.328 7.99707 17 8.66907 17 9.49707ZM16.847 14.1921C17.141 14.6601 16.999 15.2771 16.532 15.5701C15.495 16.2211 13.866 16.9971 12.001 16.9971C10.136 16.9971 8.507 16.2211 7.47 15.5701C7.002 15.2771 6.861 14.6591 7.155 14.1921C7.449 13.7251 8.066 13.5831 8.533 13.8761C9.348 14.3881 10.612 14.9971 12.002 14.9971C13.392 14.9971 14.655 14.3881 15.471 13.8761C15.937 13.5821 16.556 13.7241 16.849 14.1921H16.847Z"
        }, void 0, false, {
            fileName: "[project]/src/components/svgElements/sidebarIcon/AIbotIcon.tsx",
            lineNumber: 11,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/svgElements/sidebarIcon/AIbotIcon.tsx",
        lineNumber: 3,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = AIbotIcon;
}),
"[project]/src/utils/cookieUtils.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

/**
 * Auth token cookie management
 * Only used for authentication token storage with remember me support
 */ __turbopack_context__.s([
    "getAuthTokenCookie",
    ()=>getAuthTokenCookie,
    "removeAuthTokenCookie",
    ()=>removeAuthTokenCookie,
    "setAuthTokenCookie",
    ()=>setAuthTokenCookie
]);
const AUTH_TOKEN_COOKIE_NAME = "authToken";
const REMEMBER_ME_DAYS = 7;
const setAuthTokenCookie = (token, rememberMe = false)=>{
    let expires = "";
    if (rememberMe) {
        const date = new Date();
        date.setTime(date.getTime() + REMEMBER_ME_DAYS * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = AUTH_TOKEN_COOKIE_NAME + "=" + token + expires + "; path=/";
};
const getAuthTokenCookie = ()=>{
    const nameEQ = AUTH_TOKEN_COOKIE_NAME + "=";
    const ca = document.cookie.split(";");
    for(let i = 0; i < ca.length; i++){
        let c = ca[i];
        while(c.charAt(0) === " ")c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
};
const removeAuthTokenCookie = ()=>{
    document.cookie = AUTH_TOKEN_COOKIE_NAME + "=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";
};
}),
"[project]/src/components/commonModals/commonConfirmationModal.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$CrossIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/CrossIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/translationUtils.ts [app-ssr] (ecmascript)");
;
;
;
;
;
const CommonModal = ({ isOpen, onClose, onClick, title, description, children, showCrossIcon, isLoading = false, isSubmitting = false })=>{
    const modalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const translate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTranslate"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isOpen) return;
        const modalElement = modalRef.current;
        if (!modalElement) return;
        // Get all focusable elements within the modal
        const getFocusableElements = ()=>{
            const focusableSelectors = [
                "a[href]",
                "button:not([disabled])",
                "textarea:not([disabled])",
                "input:not([disabled])",
                "select:not([disabled])",
                "[tabindex]:not([tabindex='-1'])"
            ].join(", ");
            return Array.from(modalElement.querySelectorAll(focusableSelectors)).filter((el)=>!el.hasAttribute("disabled") && el.offsetParent !== null);
        };
        // Handle tab key press to trap focus
        const handleKeyDown = (e)=>{
            if (e.key !== "Tab") return;
            const focusableElements = getFocusableElements();
            if (focusableElements.length === 0) return;
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            // Shift + Tab (backwards)
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                }
            } else {
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        };
        // Focus first focusable element when modal opens
        const focusableElements = getFocusableElements();
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
        // Add event listener
        modalElement.addEventListener("keydown", handleKeyDown);
        // Cleanup
        return ()=>{
            modalElement.removeEventListener("keydown", handleKeyDown);
        };
    }, [
        isOpen
    ]);
    if (!isOpen) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "modal theme-modal show-modal",
        onClick: isSubmitting ? undefined : onClose,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "modal-dialog modal-dialog-centered modal-lg",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "modal-content",
                onClick: (e)=>e.stopPropagation(),
                ref: modalRef,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-header with-close-btn",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        children: title
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                        lineNumber: 113,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: description
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                        lineNumber: 114,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                lineNumber: 112,
                                columnNumber: 7
                            }, ("TURBOPACK compile-time value", void 0)),
                            showCrossIcon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                type: "button",
                                className: "clear-btn p-0",
                                onClick: onClose,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$CrossIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                    lineNumber: 119,
                                    columnNumber: 9
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                lineNumber: 118,
                                columnNumber: 8
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                        lineNumber: 111,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "modal-body",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "modal-footer",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    type: "button",
                                    className: "grey-outline-btn radius-md",
                                    onClick: onClose,
                                    disabled: isLoading,
                                    children: translate("cancel")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                    lineNumber: 125,
                                    columnNumber: 8
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    type: "button",
                                    className: "primary-btn radius-md",
                                    onClick: onClick,
                                    disabled: isLoading,
                                    loading: isLoading,
                                    children: translate("confirm")
                                }, void 0, false, {
                                    fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                                    lineNumber: 133,
                                    columnNumber: 8
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                            lineNumber: 124,
                            columnNumber: 7
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                        lineNumber: 123,
                        columnNumber: 6
                    }, ("TURBOPACK compile-time value", void 0)),
                    children
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
                lineNumber: 106,
                columnNumber: 5
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
            lineNumber: 105,
            columnNumber: 4
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/commonModals/commonConfirmationModal.tsx",
        lineNumber: 101,
        columnNumber: 3
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = CommonModal;
}),
"[project]/src/components/sidebar/Sidebar.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$logo$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$src$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object__with__data__url$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/src/assets/images/logo.svg.mjs { IMAGE => "[project]/src/assets/images/logo.svg (static in ecmascript)" } [app-ssr] (structured image object with data url, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/formElements/Button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$DashboardIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/DashboardIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$ServiceIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/ServiceIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$BookingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/BookingsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$CustomersIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/CustomersIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$StaffIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/StaffIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$PaymentsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/PaymentsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$SettingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/SettingsIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$SupportIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/SupportIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$LogoutIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/LogoutIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$AIbotIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/sidebarIcon/AIbotIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$NotificationIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/svgElements/NotificationIcon.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/react/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$cookieUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/cookieUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/translationUtils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$commonConfirmationModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/commonModals/commonConfirmationModal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Sidebar = ({ isOpen = true, onClose })=>{
    const translate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translationUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTranslate"])();
    const [isLoading, setIsLoading] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    const [showDeleteModal, setShowDeleteModal] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    const handleLogout = async ()=>{
        try {
            setIsLoading(true);
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$react$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["signOut"])({
                redirect: true,
                callbackUrl: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].LOGIN
            });
            // Delete permissions_data cookies when user logs out
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$cookieUtils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeAuthTokenCookie"])();
        } catch (error) {
            console.error("Error in logout:", error);
        } finally{
            setIsLoading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "sidebar-overlay",
                onClick: onClose,
                "aria-hidden": "true"
            }, void 0, false, {
                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                lineNumber: 53,
                columnNumber: 5
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `sidebar_main ${isOpen ? "sidebar-open" : ""}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sidebar_logo",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                            src: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$assets$2f$images$2f$logo$2e$svg$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$src$2f$assets$2f$images$2f$logo$2e$svg__$28$static__in__ecmascript$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object__with__data__url$2c$__ecmascript$29$__["default"],
                            alt: "logo",
                            className: "logo"
                        }, void 0, false, {
                            fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                            lineNumber: 57,
                            columnNumber: 6
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                        lineNumber: 56,
                        columnNumber: 5
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sidebar_mid",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        className: "active",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$DashboardIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 63,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Dashboard"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 62,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 61,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$ServiceIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 69,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Services"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 68,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 67,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$BookingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 75,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Bookings"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 74,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 73,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$CustomersIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 81,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Customers"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 80,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 79,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$StaffIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 87,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Staff"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 86,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 85,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$PaymentsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 93,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Payments"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 92,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 91,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    className: "notification-link",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$NotificationIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                isNotification: true
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 99,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Notifications"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 98,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 97,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                            lineNumber: 60,
                            columnNumber: 6
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                        lineNumber: 59,
                        columnNumber: 5
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "sidebar_bottom",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$SettingsIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 109,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Settings"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 108,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 107,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$SupportIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 115,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Support"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 114,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 113,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    className: "ai-link",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "ai-btn",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$AIbotIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 121,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "AI Training"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 120,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 119,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    className: "logout-link",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$formElements$2f$Button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        className: "logout_btn",
                                        onClick: ()=>setShowDeleteModal(true),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$svgElements$2f$sidebarIcon$2f$LogoutIcon$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                                lineNumber: 130,
                                                columnNumber: 9
                                            }, ("TURBOPACK compile-time value", void 0)),
                                            "Logout"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                        lineNumber: 126,
                                        columnNumber: 8
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                                    lineNumber: 125,
                                    columnNumber: 7
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                            lineNumber: 106,
                            columnNumber: 6
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                        lineNumber: 105,
                        columnNumber: 5
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                lineNumber: 55,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$commonModals$2f$commonConfirmationModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: showDeleteModal,
                onClose: ()=>setShowDeleteModal(false),
                title: translate("logout"),
                description: translate("logout_description"),
                showCrossIcon: false,
                isSubmitting: false,
                onClick: handleLogout,
                isLoading: isLoading
            }, void 0, false, {
                fileName: "[project]/src/components/sidebar/Sidebar.tsx",
                lineNumber: 137,
                columnNumber: 4
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = Sidebar;
}),
"[project]/src/components/layout/SidebarWrapper.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$sidebar$2f$Sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/sidebar/Sidebar.tsx [app-ssr] (ecmascript)");
"use client";
;
;
const SidebarWrapper = ()=>{
    const handleHelpCenter = ()=>{
        console.log("Help center clicked");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$sidebar$2f$Sidebar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/components/layout/SidebarWrapper.tsx",
        lineNumber: 10,
        columnNumber: 9
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = SidebarWrapper;
}),
"[project]/src/components/layout/RouteAwareWrapper.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>RouteAwareWrapper
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/routes.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/header/Header.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$SidebarWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/SidebarWrapper.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
function RouteAwareWrapper({ children }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    // Remove locale from pathname (e.g., /en/login -> /login)
    const cleanPath = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, "") || "/";
    // Check if current route is a before-login route
    const isBeforeLoginRoute = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BEFORE_LOGIN_ROUTES"].includes(cleanPath);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: isBeforeLoginRoute || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$routes$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["COMMON_ROUTES"].includes(cleanPath) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "main-container",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$SidebarWrapper$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
                    lineNumber: 29,
                    columnNumber: 6
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "main-content",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$header$2f$Header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
                            lineNumber: 31,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "inner-content",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "inner-wrapper",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "container-fluid",
                                    children: children
                                }, void 0, false, {
                                    fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
                                    lineNumber: 34,
                                    columnNumber: 9
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
                                lineNumber: 33,
                                columnNumber: 8
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
                            lineNumber: 32,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
                    lineNumber: 30,
                    columnNumber: 6
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
            lineNumber: 28,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/RouteAwareWrapper.tsx",
        lineNumber: 24,
        columnNumber: 3
    }, this);
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__6bc60ba1._.js.map