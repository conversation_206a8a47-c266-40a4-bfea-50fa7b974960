"""This file contains schema for promo codes."""
import re
from datetime import datetime, timezone
from typing import Optional

from pydantic import BaseModel, Field, field_validator

from app.models.promo_code import PromoCodeStatus


class PromoCodeBase(BaseModel):
    """Base schema for promo code data."""

    name: str = Field(..., description="Name of the promo code", min_length=4, max_length=20)
    discount_value: int = Field(..., description="Discount value (1-100)", ge=1, le=100)
    valid_from: datetime = Field(..., description="Valid from date")
    valid_until: datetime = Field(..., description="Valid until date")

    model_config = {"from_attributes": True}

    @field_validator("name")
    @classmethod
    def validate_name_format(cls, v):
        """Validate that name contains only alphanumeric characters and underscores."""
        if not re.match(r"^[a-zA-Z0-9_]+$", v):
            raise ValueError("Promo code name can only contain letters, numbers, and underscores")
        return v

    @field_validator("valid_until")
    @classmethod
    def validate_date_range(cls, v, info):
        """Validate that valid_until is on or after valid_from."""
        # Only validate that valid_until is on or after valid_from
        # Remove past date validation to allow more flexibility
        if "valid_from" in info.data and v < info.data["valid_from"]:
            raise ValueError("Valid until date must be on or after valid from date")
        return v


class PromoCodeCreate(PromoCodeBase):
    """Schema for creating a new promo code."""
    pass


class PromoCodeUpdate(BaseModel):
    """Schema for updating promo code status."""

    id: int = Field(..., description="Promo code ID")
    status: str = Field(..., description="New status for the promo code")

    @field_validator("status")
    @classmethod
    def validate_status(cls, v):
        """Validate that status is a valid PromoCodeStatus enum value."""
        valid_statuses = [status.value for status in PromoCodeStatus]
        if v not in valid_statuses:
            raise ValueError(f"Invalid status value. Must be one of {valid_statuses}")
        return v


class PromoCodeResponse(PromoCodeBase):
    """Schema for promo code response."""

    id: int = Field(..., description="Promo code ID")
    status: str = Field(..., description="Status of the promo code")
    expired: bool = Field(..., description="Whether the promo code is expired")
    inactive: bool = Field(..., description="Whether the promo code is inactive")
    active: bool = Field(..., description="Whether the promo code is active")
    created_at: str = Field(..., description="Timestamp when the promo code was created")
    updated_at: Optional[str] = Field(None, description="Timestamp when the promo code was last updated")

    model_config = {"from_attributes": True}

    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def convert_datetime_to_str(cls, v):
        """Convert datetime to string."""
        if isinstance(v, datetime):
            return v.isoformat()
        return v


class PromoCodeUpdateDates(BaseModel):
    """Schema for updating promo code dates."""

    valid_from: datetime = Field(..., description="New valid from date")
    valid_until: datetime = Field(..., description="New valid until date")

    model_config = {"from_attributes": True}

    @field_validator("valid_until")
    @classmethod
    def validate_date_range(cls, v, info):
        """Validate that valid_until is on or after valid_from."""
        if "valid_from" in info.data and v < info.data["valid_from"]:
            raise ValueError("Valid until date must be on or after valid from date")
        return v
